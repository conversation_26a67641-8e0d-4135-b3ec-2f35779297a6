import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/pet.dart';
import '../constants/pet_breeds.dart';
import '../utils/toast_utils.dart';
import '../utils/app_logger.dart';
import '../widgets/pet_header_image.dart';
import '../widgets/pet_species_selector.dart';
import '../widgets/pet_breed_selector.dart';
import '../widgets/pet_basic_info_form.dart';
import '../providers/auth_provider.dart';

class PetInfoInputScreen extends StatefulWidget {
  final Pet? existingPet; // 用于编辑模式

  const PetInfoInputScreen({Key? key, this.existingPet}) : super(key: key);

  @override
  State<PetInfoInputScreen> createState() => _PetInfoInputScreenState();
}

class _PetInfoInputScreenState extends State<PetInfoInputScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nicknameController = TextEditingController();
  final _shoulderHeightController = TextEditingController();
  final _weightController = TextEditingController();
  final _customBreedController = TextEditingController();

  PetSpecies _selectedSpecies = PetSpecies.dog;
  String? _selectedBreed;
  DateTime _selectedBirthday =
      DateTime.now().subtract(const Duration(days: 365));
  bool _isCustomBreed = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    if (widget.existingPet != null) {
      final pet = widget.existingPet!;
      _nicknameController.text = pet.nickname;
      _selectedSpecies = pet.species;
      _selectedBreed = pet.breed;
      _selectedBirthday = pet.birthday;
      _shoulderHeightController.text = pet.shoulderHeight.toString();
      _weightController.text = pet.weight.toString();

      // 检查是否为自定义品种
      _isCustomBreed = !PetBreeds.isPresetBreed(pet.species.value, pet.breed);
      if (_isCustomBreed) {
        _customBreedController.text = pet.breed;
        _selectedBreed = '其他';
      }
    }
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _shoulderHeightController.dispose();
    _weightController.dispose();
    _customBreedController.dispose();
    super.dispose();
  }

  void _selectBirthday() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedBirthday,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      locale: const Locale('zh', 'CN'),
    );
    if (picked != null && picked != _selectedBirthday) {
      setState(() {
        _selectedBirthday = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingPet != null ? '编辑爱宠信息' : '添加爱宠信息'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.orange.shade300,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.only(bottom: 20),
          child: Column(
            children: [
              PetHeaderImage(selectedSpecies: _selectedSpecies),
              PetSpeciesSelector(
                selectedSpecies: _selectedSpecies,
                onSpeciesChanged: (species) {
                  setState(() {
                    _selectedSpecies = species;
                    _selectedBreed = null;
                    _isCustomBreed = false;
                    _customBreedController.clear();
                  });
                },
              ),
              PetBreedSelector(
                selectedSpecies: _selectedSpecies,
                selectedBreed: _selectedBreed,
                isCustomBreed: _isCustomBreed,
                customBreedController: _customBreedController,
                onBreedChanged: (breed) {
                  setState(() {
                    _selectedBreed = breed;
                  });
                },
                onCustomBreedChanged: (isCustom) {
                  setState(() {
                    _isCustomBreed = isCustom;
                  });
                },
              ),
              PetBasicInfoForm(
                nicknameController: _nicknameController,
                shoulderHeightController: _shoulderHeightController,
                weightController: _weightController,
                selectedBirthday: _selectedBirthday,
                onBirthdayTap: _selectBirthday,
              ),
              const SizedBox(height: 32),
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 30),
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _submitForm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade400,
                    foregroundColor: Colors.white,
                    // padding: const EdgeInsets.symmetric(vertical: 16),
                    // shape: RoundedRectangleBorder(
                    //   borderRadius: BorderRadius.circular(12),
                    // ),
                    // elevation: 2,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          widget.existingPet != null ? '更新信息' : '完成',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submitForm() async {
    // 详细的表单验证
    if (!_validateForm()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 获取最终的品种名称
      final finalBreed =
          _isCustomBreed ? _customBreedController.text.trim() : _selectedBreed!;

      // 创建Pet对象，保留现有的ID（如果是编辑模式）
      final pet = Pet(
        id: widget.existingPet?.id,
        nickname: _nicknameController.text.trim(),
        species: _selectedSpecies,
        breed: finalBreed,
        birthday: _selectedBirthday,
        shoulderHeight: double.parse(_shoulderHeightController.text),
        weight: double.parse(_weightController.text),
      );

      // 通过 AuthProvider 保存宠物信息（包含 API 调用和本地存储）
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.savePet(pet);

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(widget.existingPet != null ? '宠物信息更新成功！' : '宠物信息添加成功！'),
            backgroundColor: Colors.green,
          ),
        );

        // 返回上一页，传递 true 表示操作成功
        Navigator.pop(context, true);
      }
    } catch (e) {
      AppLogger.error('保存宠物信息失败', error: e);
      if (mounted) {
        ToastUtils.showError('保存失败：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _validateForm() {
    if (!_formKey.currentState!.validate()) {
      ToastUtils.showError('请检查输入信息');
      return false;
    }

    if (_selectedBreed == null) {
      ToastUtils.showError('请选择宠物品种');
      return false;
    }

    if (_isCustomBreed && _customBreedController.text.trim().isEmpty) {
      ToastUtils.showError('请输入自定义品种名称');
      return false;
    }

    return true;
  }
}
