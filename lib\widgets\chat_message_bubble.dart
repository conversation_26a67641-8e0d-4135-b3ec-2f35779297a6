import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:intl/intl.dart';
import '../models/chat_message.dart';
import '../providers/chatbot_provider.dart';
import 'package:provider/provider.dart';

/// 聊天消息气泡组件
///
/// 区分用户消息和AI消息的显示样式，支持Markdown渲染和错误显示
class ChatMessageBubble extends StatelessWidget {
  /// 聊天消息
  final ChatMessage message;

  /// 消息状态更新回调（用于打字机效果完成后更新状态）
  final Function(String messageId, MessageStatus status)? onStatusUpdate;

  /// 重发消息回调
  final Function(ChatMessage message)? onResend;

  /// 是否显示时间戳
  final bool showTimestamp;

  /// 是否显示头像
  final bool showAvatar;

  const ChatMessageBubble({
    Key? key,
    required this.message,
    this.onStatusUpdate,
    this.onResend,
    this.showTimestamp = true,
    this.showAvatar = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Column(
        crossAxisAlignment:
            message.isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // 时间戳
          if (showTimestamp) _buildTimestamp(),

          // 消息内容
          Row(
            mainAxisAlignment: message.isUser
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // AI头像（左侧）
              if (!message.isUser && showAvatar) _buildAvatar(),

              // 消息气泡
              Flexible(child: _buildMessageBubble(context)),

              // 用户头像（右侧）
              if (message.isUser && showAvatar) _buildAvatar(),
            ],
          ),

          // 消息状态指示器和错误信息
          if (message.isUser) _buildStatusIndicator(context),
          if (!message.isUser) _buildErrorMessage(context),
        ],
      ),
    );
  }

  /// 构建时间戳
  Widget _buildTimestamp() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        DateFormat('HH:mm').format(message.timestamp),
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar() {
    return Container(
      margin: EdgeInsets.only(
        left: message.isUser ? 4 : 0,
        right: message.isUser ? 0 : 4,
        top: 2,
      ),
      child: CircleAvatar(
        radius: 12,
        backgroundColor: message.isUser ? Colors.blue[100] : Colors.orange[100],
        child: Icon(
          message.isUser ? Icons.person : Icons.smart_toy,
          size: 14,
          color: message.isUser ? Colors.blue[700] : Colors.orange[700],
        ),
      ),
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.85,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getBubbleColor(),
        borderRadius: _getBubbleBorderRadius(),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _buildMessageContent(context),
    );
  }

  /// 获取气泡颜色
  Color _getBubbleColor() {
    if (message.isUser) {
      return Colors.blue[500]!;
    } else {
      return Colors.grey[100]!;
    }
  }

  /// 获取气泡圆角
  BorderRadius _getBubbleBorderRadius() {
    const radius = Radius.circular(16);
    const smallRadius = Radius.circular(4);

    if (message.isUser) {
      return const BorderRadius.only(
        topLeft: radius,
        topRight: radius,
        bottomLeft: radius,
        bottomRight: smallRadius,
      );
    } else {
      return const BorderRadius.only(
        topLeft: radius,
        topRight: radius,
        bottomLeft: smallRadius,
        bottomRight: radius,
      );
    }
  }

  /// 构建消息内容
  Widget _buildMessageContent(BuildContext context) {
    final textStyle = TextStyle(
      fontSize: 16,
      color: message.isUser ? Colors.white : Colors.black87,
      height: 1.4,
    );

    // 用户消息使用普通文本显示
    if (message.isUser) {
      return Text(
        message.content,
        style: textStyle,
      );
    }

    // AI消息直接使用Markdown渲染（移除打字机效果）
    return _buildMarkdownContent(context, textStyle);
  }

  /// 构建Markdown内容
  Widget _buildMarkdownContent(BuildContext context, TextStyle baseStyle) {
    if (message.content.isEmpty) {
      return Text(
        '正在思考中...',
        style: baseStyle.copyWith(
          fontStyle: FontStyle.italic,
          color: Colors.grey[600],
        ),
      );
    }

    return MarkdownBody(
      data: message.content,
      selectable: true,
      styleSheet: _buildOptimizedMarkdownStyleSheet(baseStyle),
      onTapLink: (text, href, title) {
        // 处理链接点击
        if (href != null) {
          _handleLinkTap(context, href);
        }
      },
    );
  }

  /// 构建优化的Markdown样式表
  MarkdownStyleSheet _buildOptimizedMarkdownStyleSheet(TextStyle baseStyle) {
    return MarkdownStyleSheet(
      // 段落样式 - 增加行间距提升可读性
      p: baseStyle.copyWith(
        height: 1.6,
        letterSpacing: 0.2,
      ),

      // 标题样式 - 层次分明，间距合理
      h1: baseStyle.copyWith(
        fontSize: 22,
        fontWeight: FontWeight.w700,
        height: 1.4,
        color: Colors.black87,
      ),
      h2: baseStyle.copyWith(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        height: 1.4,
        color: Colors.black87,
      ),
      h3: baseStyle.copyWith(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        height: 1.4,
        color: Colors.black87,
      ),
      h4: baseStyle.copyWith(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        height: 1.4,
        color: Colors.black87,
      ),
      h5: baseStyle.copyWith(
        fontSize: 15,
        fontWeight: FontWeight.w500,
        height: 1.4,
        color: Colors.black87,
      ),
      h6: baseStyle.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        height: 1.4,
        color: Colors.black87,
      ),

      // 强调样式
      strong: baseStyle.copyWith(
        fontWeight: FontWeight.w700,
        color: Colors.black87,
      ),
      em: baseStyle.copyWith(
        fontStyle: FontStyle.italic,
        color: Colors.black87,
      ),

      // 内联代码样式 - 更清晰的视觉区分
      code: TextStyle(
        fontSize: baseStyle.fontSize! * 0.9,
        color: const Color(0xFF2D3748),
        backgroundColor: const Color(0xFFF7FAFC),
        fontFamily: 'Consolas, Monaco, "Courier New", monospace',
        letterSpacing: 0.5,
      ),

      // 代码块样式 - 专业的代码显示
      codeblockDecoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      codeblockPadding: const EdgeInsets.all(16),

      // 引用块样式 - 优雅的引用显示
      blockquote: baseStyle.copyWith(
        color: const Color(0xFF4A5568),
        fontStyle: FontStyle.italic,
        height: 1.6,
      ),
      blockquoteDecoration: BoxDecoration(
        color: const Color(0xFFF0F4F8),
        borderRadius: BorderRadius.circular(4),
        border: const Border(
          left: BorderSide(
            color: Color(0xFF3182CE),
            width: 4,
          ),
        ),
      ),
      blockquotePadding: const EdgeInsets.fromLTRB(16, 12, 16, 12),

      // 列表样式 - 清晰的层次结构
      listBullet: baseStyle.copyWith(
        height: 1.6,
        color: const Color(0xFF2D3748),
      ),
      listIndent: 24,

      // 表格样式 - 专业的表格显示
      tableHead: baseStyle.copyWith(
        fontWeight: FontWeight.w600,
        color: const Color(0xFF2D3748),
        backgroundColor: const Color(0xFFF7FAFC),
      ),
      tableBody: baseStyle.copyWith(
        height: 1.5,
        color: const Color(0xFF4A5568),
      ),
      tableBorder: TableBorder.all(
        color: const Color(0xFFE2E8F0),
        width: 1,
      ),
      tableColumnWidth: const FlexColumnWidth(),
      tableCellsPadding: const EdgeInsets.all(12),

      // 水平分割线
      horizontalRuleDecoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: const Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
      ),

      // 链接样式
      a: baseStyle.copyWith(
        color: const Color(0xFF3182CE),
        decoration: TextDecoration.underline,
      ),
    );
  }

  /// 处理链接点击
  void _handleLinkTap(BuildContext context, String url) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('打开链接'),
        content: Text('是否要打开链接：\n$url'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // 这里可以使用 url_launcher 包来打开链接
              // launch(url);
            },
            child: const Text('打开'),
          ),
        ],
      ),
    );
  }

  /// 构建错误信息（AI消息）
  Widget _buildErrorMessage(BuildContext context) {
    return Consumer<ChatbotProvider>(
      builder: (context, provider, child) {
        final errorMessage = provider.getMessageError(message.id);

        if (errorMessage == null || errorMessage.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: EdgeInsets.only(
            top: 8,
            left: showAvatar ? 28 : 0,
            right: 40,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                size: 16,
                color: Colors.red[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  errorMessage,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.red[700],
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建状态指示器（仅用户消息）
  Widget _buildStatusIndicator(BuildContext context) {
    if (!message.isUser) return const SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.only(top: 4, right: showAvatar ? 40 : 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildStatusIcon(),
          if (message.status == MessageStatus.failed) ...[
            const SizedBox(width: 8),
            _buildResendButton(context),
          ],
        ],
      ),
    );
  }

  /// 构建状态图标
  Widget _buildStatusIcon() {
    switch (message.status) {
      case MessageStatus.sending:
        return SizedBox(
          width: 12,
          height: 12,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
          ),
        );

      case MessageStatus.sent:
        return Icon(
          Icons.check,
          size: 16,
          color: Colors.grey[600],
        );

      case MessageStatus.failed:
        return Icon(
          Icons.error_outline,
          size: 16,
          color: Colors.red[400],
        );

      case MessageStatus.typing:
        return const SizedBox.shrink();
    }
  }

  /// 构建重发按钮
  Widget _buildResendButton(BuildContext context) {
    return GestureDetector(
      onTap: () => onResend?.call(message),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.refresh,
              size: 14,
              color: Colors.red[600],
            ),
            const SizedBox(width: 4),
            Text(
              '重发',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 系统消息气泡（用于显示系统提示信息）
class SystemMessageBubble extends StatelessWidget {
  final String message;
  final IconData? icon;
  final Color? color;

  const SystemMessageBubble({
    Key? key,
    required this.message,
    this.icon,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: (color ?? Colors.grey[300])!.withOpacity(0.3),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 16,
                  color: color ?? Colors.grey[600],
                ),
                const SizedBox(width: 8),
              ],
              Text(
                message,
                style: TextStyle(
                  fontSize: 14,
                  color: color ?? Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
