import 'package:flutter/material.dart';

/// 聊天输入框组件
/// 
/// 遵循扁平化设计风格，提供消息输入和发送功能
class ChatInputField extends StatefulWidget {
  /// 发送消息回调
  final Function(String message) onSendMessage;
  
  /// 是否正在发送消息
  final bool isSending;
  
  /// 输入框提示文本
  final String hintText;
  
  /// 最大行数
  final int maxLines;
  
  /// 是否启用
  final bool enabled;

  const ChatInputField({
    Key? key,
    required this.onSendMessage,
    this.isSending = false,
    this.hintText = '输入消息...',
    this.maxLines = 4,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends State<ChatInputField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  /// 是否有输入内容
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 文本变化监听
  void _onTextChanged() {
    final hasText = _controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  /// 发送消息
  void _sendMessage() {
    final message = _controller.text.trim();
    if (message.isNotEmpty && !widget.isSending) {
      widget.onSendMessage(message);
      _controller.clear();
      _focusNode.requestFocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // 输入框
            Expanded(child: _buildInputField()),
            
            const SizedBox(width: 12),
            
            // 发送按钮
            _buildSendButton(),
          ],
        ),
      ),
    );
  }

  /// 构建输入框
  Widget _buildInputField() {
    return Container(
      constraints: BoxConstraints(
        minHeight: 44,
        maxHeight: widget.maxLines * 24.0 + 20, // 估算最大高度
      ),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(22),
        border: Border.all(
          color: _focusNode.hasFocus 
              ? Colors.orange.withOpacity(0.3)
              : Colors.transparent,
          width: 1,
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled && !widget.isSending,
        maxLines: null,
        minLines: 1,
        textInputAction: TextInputAction.send,
        onSubmitted: (_) => _sendMessage(),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 16,
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          // 添加前缀图标（可选）
          prefixIcon: Icon(
            Icons.chat_bubble_outline,
            color: Colors.grey.shade500,
            size: 20,
          ),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          height: 1.4,
        ),
      ),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    final canSend = _hasText && !widget.isSending && widget.enabled;
    
    return GestureDetector(
      onTap: canSend ? _sendMessage : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: canSend 
              ? Colors.orange.shade400 
              : Colors.grey.shade300,
          borderRadius: BorderRadius.circular(22),
          boxShadow: canSend ? [
            BoxShadow(
              color: Colors.orange.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: widget.isSending
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Icon(
                Icons.send_rounded,
                color: canSend ? Colors.white : Colors.grey.shade500,
                size: 20,
              ),
      ),
    );
  }
}

/// 带附加功能的聊天输入框
class AdvancedChatInputField extends StatefulWidget {
  /// 发送消息回调
  final Function(String message) onSendMessage;
  
  /// 是否正在发送消息
  final bool isSending;
  
  /// 输入框提示文本
  final String hintText;
  
  /// 是否显示附加功能按钮
  final bool showAttachments;
  
  /// 附件按钮点击回调
  final VoidCallback? onAttachmentTap;
  
  /// 语音按钮点击回调
  final VoidCallback? onVoiceTap;

  const AdvancedChatInputField({
    Key? key,
    required this.onSendMessage,
    this.isSending = false,
    this.hintText = '输入消息...',
    this.showAttachments = false,
    this.onAttachmentTap,
    this.onVoiceTap,
  }) : super(key: key);

  @override
  State<AdvancedChatInputField> createState() => _AdvancedChatInputFieldState();
}

class _AdvancedChatInputFieldState extends State<AdvancedChatInputField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _sendMessage() {
    final message = _controller.text.trim();
    if (message.isNotEmpty && !widget.isSending) {
      widget.onSendMessage(message);
      _controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 附加功能栏
            if (widget.showAttachments) _buildAttachmentBar(),
            
            // 输入栏
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 附件按钮
                if (widget.showAttachments) _buildAttachmentButton(),
                
                // 输入框
                Expanded(child: _buildInputField()),
                
                const SizedBox(width: 8),
                
                // 发送/语音按钮
                _hasText ? _buildSendButton() : _buildVoiceButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentBar() {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          _buildQuickActionButton(
            icon: Icons.photo_camera,
            label: '相机',
            onTap: () {},
          ),
          _buildQuickActionButton(
            icon: Icons.photo_library,
            label: '相册',
            onTap: () {},
          ),
          _buildQuickActionButton(
            icon: Icons.location_on,
            label: '位置',
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.grey.shade600),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentButton() {
    return GestureDetector(
      onTap: widget.onAttachmentTap,
      child: Container(
        width: 36,
        height: 36,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(18),
        ),
        child: Icon(
          Icons.add,
          color: Colors.grey.shade600,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildInputField() {
    return Container(
      constraints: const BoxConstraints(
        minHeight: 36,
        maxHeight: 100,
      ),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(18),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        maxLines: null,
        minLines: 1,
        textInputAction: TextInputAction.send,
        onSubmitted: (_) => _sendMessage(),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: TextStyle(color: Colors.grey.shade600),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
        ),
      ),
    );
  }

  Widget _buildSendButton() {
    return GestureDetector(
      onTap: _sendMessage,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: Colors.orange.shade400,
          borderRadius: BorderRadius.circular(18),
        ),
        child: const Icon(
          Icons.send,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildVoiceButton() {
    return GestureDetector(
      onTap: widget.onVoiceTap,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(18),
        ),
        child: Icon(
          Icons.mic,
          color: Colors.grey.shade600,
          size: 18,
        ),
      ),
    );
  }
}
