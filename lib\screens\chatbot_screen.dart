import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/chatbot_provider.dart';
import '../models/chat_message.dart';
import '../widgets/chat_message_bubble.dart';
import '../widgets/chat_input_field.dart';

/// AI聊天机器人界面
class ChatbotScreen extends StatefulWidget {
  const ChatbotScreen({Key? key}) : super(key: key);

  @override
  State<ChatbotScreen> createState() => _ChatbotScreenState();
}

class _ChatbotScreenState extends State<ChatbotScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 只在第一次进入时初始化聊天Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<ChatbotProvider>();
      // 使用智能初始化，避免重复清空历史记录
      provider.init();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  /// 发送消息
  void _sendMessage(String message) async {
    final provider = context.read<ChatbotProvider>();
    await provider.sendMessage(message);
    _scrollToBottom();
  }

  /// 重发消息
  void _resendMessage(ChatMessage message) async {
    final provider = context.read<ChatbotProvider>();
    await provider.resendMessage(message);
    _scrollToBottom();
  }

  /// 更新消息状态（已移除，不再需要）
  void _updateMessageStatus(String messageId, MessageStatus status) {
    // 不再需要更新消息状态，因为已移除打字机效果
  }

  /// 新建对话
  void _createNewConversation() async {
    final confirmed = await _showNewConversationDialog();
    if (confirmed == true) {
      await context.read<ChatbotProvider>().createNewConversation();
    }
  }

  /// 显示新建对话确认对话框
  Future<bool?> _showNewConversationDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('新建对话'),
        content: const Text('确定要开始新的对话吗？当前对话将被清空。'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              '取消',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade400,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // 消息列表
          Expanded(child: _buildMessageList()),

          // 输入框
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Consumer<ChatbotProvider>(
        builder: (context, provider, child) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '养宠小博士',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
              if (provider.hasMessages) ...[
                const SizedBox(height: 2),
                Text(
                  '会话 ${provider.currentSessionId.substring(0, 8)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ],
          );
        },
      ),
      backgroundColor: Colors.white,
      foregroundColor: Colors.black87,
      elevation: 0,
      centerTitle: true,
      actions: [
        // 新建对话按钮
        Container(
          margin: const EdgeInsets.only(right: 16),
          child: GestureDetector(
            onTap: _createNewConversation,
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.orange.shade400,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建消息列表
  Widget _buildMessageList() {
    return Consumer<ChatbotProvider>(
      builder: (context, provider, child) {
        if (provider.messages.isEmpty) {
          return _buildEmptyState();
        }

        return StreamBuilder<List<ChatMessage>>(
          stream: Stream.periodic(
            const Duration(milliseconds: 100),
            (_) => provider.messages,
          ).distinct(),
          initialData: provider.messages,
          builder: (context, snapshot) {
            final messages = snapshot.data ?? [];

            return ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(vertical: 16),
              itemCount: messages.length +
                  (_shouldShowTypingIndicator(provider, messages) ? 1 : 0),
              itemBuilder: (context, index) {
                // AI正在输入指示器
                if (index == messages.length &&
                    _shouldShowTypingIndicator(provider, messages)) {
                  return _buildTypingIndicator();
                }

                final message = messages[index];
                return ChatMessageBubble(
                  message: message,
                  onStatusUpdate: _updateMessageStatus,
                  onResend: _resendMessage,
                );
              },
            );
          },
        );
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.smart_toy,
              size: 40,
              color: Colors.orange.shade400,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            '你好！我是养宠小博士',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '我可以为您提供宠物护理建议\n健康咨询等服务',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: 32),
          _buildQuickActions(),
        ],
      ),
    );
  }

  /// 构建快捷操作
  Widget _buildQuickActions() {
    final quickActions = [
      {'text': '宠物健康咨询', 'icon': Icons.favorite},
      {'text': '喂养建议', 'icon': Icons.restaurant},
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: quickActions.map((action) {
        return GestureDetector(
          onTap: () => _sendMessage(action['text'] as String),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.orange.shade200),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  action['icon'] as IconData,
                  size: 16,
                  color: Colors.orange.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  action['text'] as String,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 判断是否应该显示typing indicator
  /// 只有在AI正在输入且没有AI消息正在流式输出时才显示
  bool _shouldShowTypingIndicator(
      ChatbotProvider provider, List<ChatMessage> messages) {
    // 如果AI不在输入状态，不显示
    if (!provider.isAITyping) return false;

    // 如果消息列表为空，显示
    if (messages.isEmpty) return true;

    // 如果最后一条消息是AI消息且内容为空，说明AI消息占位符已创建，不显示typing indicator
    final lastMessage = messages.last;
    if (!lastMessage.isUser && lastMessage.content.isEmpty) {
      return false;
    }

    // 如果最后一条消息是AI消息且有内容，说明正在流式输出，不显示typing indicator
    if (!lastMessage.isUser && lastMessage.content.isNotEmpty) {
      return false;
    }

    // 其他情况显示typing indicator
    return true;
  }

  /// 构建AI正在输入指示器
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        children: [
          // AI头像
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.orange[100],
            child: Icon(
              Icons.smart_toy,
              size: 18,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(width: 8),

          // 输入指示器
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
                bottomLeft: Radius.circular(4),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypingDot(0),
                const SizedBox(width: 4),
                _buildTypingDot(1),
                const SizedBox(width: 4),
                _buildTypingDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建输入指示器的点
  Widget _buildTypingDot(int index) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(milliseconds: 600 + index * 200),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.5 + (value * 0.5),
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        );
      },
    );
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Consumer<ChatbotProvider>(
      builder: (context, provider, child) {
        return ChatInputField(
          onSendMessage: _sendMessage,
          isSending: provider.isSending,
          enabled: !provider.isAITyping,
          hintText: provider.isAITyping ? 'AI正在回复中...' : '输入消息...',
        );
      },
    );
  }
}
