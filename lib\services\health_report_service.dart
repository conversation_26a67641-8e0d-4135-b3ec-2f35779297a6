import 'dart:async';
import 'dart:convert';
import 'package:eventflux/eventflux.dart';
import '../widgets/time_period_selector.dart';
import '../constants/url_form.dart';
import 'package:provider/provider.dart';
import '../constants/constants.dart';
import '../providers/auth_provider.dart';
import '../utils/app_logger.dart';

/// 健康报告服务类
/// 负责通过SSE接口获取健康分析报告数据
class HealthReportService {
  EventFlux? _currentEventFlux;

  /// 获取健康报告
  ///
  /// [deviceName] 设备名称
  /// [period] 时间段类型
  /// [date] 选择的日期
  /// [onData] 数据接收回调
  /// [onError] 错误处理回调
  /// [onDone] 完成回调
  Future<void> getHealthReport({
    required String deviceName,
    required TimePeriod period,
    required DateTime date,
    required Function(String) onData,
    required Function(String) onError,
    required Function() onDone,
  }) async {
    try {
      // 关闭之前的连接
      await dispose();

      // 获取JWT token
      String? accessToken;
      try {
        final context = navigatorKey.currentContext;
        if (context == null) {
          onError('应用上下文不可用，请重试');
          return;
        }
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        accessToken = authProvider.user?.accessToken;
      } catch (e) {
        AppLogger.warning('获取用户token失败: $e');
        onError('用户未登录，请重新登录');
        return;
      }

      if (accessToken == null || accessToken.isEmpty) {
        onError('用户未登录，请重新登录');
        return;
      }

      // 构建请求体
      final requestBody = {
        'device_name': deviceName,
        'period': period.name,
        'date': _formatDate(date, period),
      };

      // 构建请求头
      final headers = {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final url = UrlFormat.HealthReport;

      AppLogger.info('健康报告SSE请求URL: $url');
      AppLogger.info('请求体: ${jsonEncode(requestBody)}');

      // 创建EventFlux实例
      final eventFlux = EventFlux.spawn();

      // 连接到SSE流
      eventFlux.connect(
        EventFluxConnectionType.post,
        url,
        header: headers,
        body: requestBody,
        tag: 'HealthReportSSE',
        logReceivedData: false,
        onSuccessCallback: (EventFluxResponse? response) {
          AppLogger.info('健康报告EventFlux连接成功');

          // 监听数据流
          response?.stream?.listen(
            (EventFluxData data) {
              try {
                AppLogger.info('接收到健康报告SSE事件: ${data.data}');

                // 解析SSE数据
                if (data.data != null && data.data.isNotEmpty) {
                  final jsonData = jsonDecode(data.data);

                  // 处理不同类型的事件
                  if (jsonData['type'] == 'content') {
                    final content = jsonData['content'] ?? '';
                    if (content.isNotEmpty) {
                      onData(content);
                    }
                  } else if (jsonData['type'] == 'error') {
                    final errorMsg = jsonData['error'] ?? '未知错误';
                    onError(errorMsg);
                  } else if (jsonData['type'] == 'done') {
                    onDone();
                  }
                }
              } catch (e, stackTrace) {
                AppLogger.error('解析健康报告SSE数据失败: $e',
                    error: e, stackTrace: stackTrace);
                onError('数据解析错误，请重试');
              }
            },
            onError: (error) {
              AppLogger.error('健康报告SSE数据流错误: $error', error: error);
              onError('数据流错误，请重试');
            },
            onDone: () {
              AppLogger.info('健康报告SSE数据流结束');
              onDone();
            },
          );
        },
        onError: (EventFluxException? error) {
          AppLogger.error('健康报告EventFlux连接错误: ${error?.message}',
              error: error ?? 'Unknown error');
          onError('网络连接错误，请检查网络后重试');
        },
        onConnectionClose: () {
          AppLogger.info('健康报告EventFlux连接关闭');
          onDone();
        },
      );

      // 保存当前连接引用
      _currentEventFlux = eventFlux;
    } catch (e, stackTrace) {
      AppLogger.error('创建健康报告SSE连接失败: $e', error: e, stackTrace: stackTrace);
      onError('连接失败，请检查网络设置');
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        // 获取周的开始日期（周一）
        final startOfWeek = date.subtract(Duration(days: date.weekday - 1));
        return '${startOfWeek.year}-${startOfWeek.month.toString().padLeft(2, '0')}-${startOfWeek.day.toString().padLeft(2, '0')}';
      case TimePeriod.month:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}';
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    if (_currentEventFlux != null) {
      AppLogger.info('释放健康报告EventFlux连接');
      await _currentEventFlux!.disconnect();
      _currentEventFlux = null;
    }
  }
}
