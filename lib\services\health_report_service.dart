import 'dart:async';
import 'dart:convert';
import 'package:eventflux/eventflux.dart';
import '../widgets/time_period_selector.dart';
import '../constants/url_form.dart';
import '../utils/storage_utils.dart';

/// 健康报告服务类
/// 负责通过SSE接口获取健康分析报告数据
class HealthReportService {
  EventFlux? _eventFlux;
  StreamSubscription? _subscription;

  /// 获取健康报告
  /// 
  /// [deviceName] 设备名称
  /// [period] 时间段类型
  /// [date] 选择的日期
  /// [onData] 数据接收回调
  /// [onError] 错误处理回调
  /// [onDone] 完成回调
  Future<void> getHealthReport({
    required String deviceName,
    required TimePeriod period,
    required DateTime date,
    required Function(String) onData,
    required Function(String) onError,
    required Function() onDone,
  }) async {
    try {
      // 关闭之前的连接
      await dispose();

      // 获取JWT token
      final token = await StorageUtils.getJwtToken();
      if (token == null || token.isEmpty) {
        onError('用户未登录，请重新登录');
        return;
      }

      // 构建请求URL
      final url = _buildRequestUrl(deviceName, period, date);
      
      // 创建SSE连接
      _eventFlux = EventFlux.connect(
        EventFluxConnectionType.get,
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        autoReconnect: false,
      );

      // 监听数据流
      _subscription = _eventFlux!.stream.listen(
        (event) {
          try {
            // 处理SSE事件
            if (event.event == 'data') {
              // 解析数据
              final data = event.data;
              if (data != null && data.isNotEmpty) {
                onData(data);
              }
            } else if (event.event == 'error') {
              // 处理错误事件
              final errorData = event.data ?? '未知错误';
              onError(errorData);
            } else if (event.event == 'done') {
              // 处理完成事件
              onDone();
            }
          } catch (e) {
            onError('数据解析错误: $e');
          }
        },
        onError: (error) {
          onError('连接错误: $error');
        },
        onDone: () {
          onDone();
        },
      );

    } catch (e) {
      onError('服务连接失败: $e');
    }
  }

  /// 构建请求URL
  String _buildRequestUrl(String deviceName, TimePeriod period, DateTime date) {
    final baseUrl = '${UrlFormat.apiAI}/health_report/';
    
    // 格式化日期
    final dateStr = _formatDate(date, period);
    
    // 构建查询参数
    final queryParams = {
      'device_name': deviceName,
      'period': period.name,
      'date': dateStr,
    };
    
    final queryString = queryParams.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
    
    return '$baseUrl?$queryString';
  }

  /// 格式化日期
  String _formatDate(DateTime date, TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        // 获取周的开始日期（周一）
        final startOfWeek = date.subtract(Duration(days: date.weekday - 1));
        return '${startOfWeek.year}-${startOfWeek.month.toString().padLeft(2, '0')}-${startOfWeek.day.toString().padLeft(2, '0')}';
      case TimePeriod.month:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}';
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await _subscription?.cancel();
    _subscription = null;
    
    await _eventFlux?.disconnect();
    _eventFlux = null;
  }
}

/// 健康报告数据模型
class HealthReportData {
  final String content;
  final bool isComplete;
  final String? error;

  HealthReportData({
    required this.content,
    this.isComplete = false,
    this.error,
  });

  factory HealthReportData.fromJson(Map<String, dynamic> json) {
    return HealthReportData(
      content: json['content'] ?? '',
      isComplete: json['is_complete'] ?? false,
      error: json['error'],
    );
  }
}

/// 健康报告响应模型
class HealthReportResponse {
  final String event;
  final String data;
  final String? id;

  HealthReportResponse({
    required this.event,
    required this.data,
    this.id,
  });

  factory HealthReportResponse.fromEventData(String eventData) {
    final lines = eventData.split('\n');
    String event = 'message';
    String data = '';
    String? id;

    for (final line in lines) {
      if (line.startsWith('event:')) {
        event = line.substring(6).trim();
      } else if (line.startsWith('data:')) {
        data = line.substring(5).trim();
      } else if (line.startsWith('id:')) {
        id = line.substring(3).trim();
      }
    }

    return HealthReportResponse(
      event: event,
      data: data,
      id: id,
    );
  }
}

/// 健康报告错误类型
enum HealthReportErrorType {
  networkError,
  authError,
  serverError,
  parseError,
  unknownError,
}

/// 健康报告异常类
class HealthReportException implements Exception {
  final String message;
  final HealthReportErrorType type;
  final dynamic originalError;

  HealthReportException({
    required this.message,
    required this.type,
    this.originalError,
  });

  @override
  String toString() {
    return 'HealthReportException: $message (Type: $type)';
  }
}
