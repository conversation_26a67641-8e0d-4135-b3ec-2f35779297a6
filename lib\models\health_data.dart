import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/user_context.dart';
import '../constants/activity_status.dart';
import '../constants/emotion_status.dart';
import '../constants/health_detection.dart';

/// 健康数据模型，用于表示宠物的健康监测数据。
class HealthData {
  // 基础数据（保持与原HealthData兼容）
  final int stepCount;
  final double temperature;
  final DateTime datetime;
  // 扩展数据
  final double calories; // 卡路里消耗
  final List<ActivityStatus> activityStatus; // 当前活动状态列表
  final EmotionStatus emotionStatus; // 当前情绪状态
  final List<HealthDetection> healthDetections; // 健康检测结果集合

  // 健康数据模型的构造函数，包含数据的唯一标识符、宠物标识符、活动量、温度和时间戳
  HealthData({
    required this.stepCount,
    required this.temperature,
    required this.datetime,
    required this.calories,
    required this.activityStatus,
    required this.emotionStatus,
    required this.healthDetections,
  });

  /// 从 JSON 数据创建 HealthData 实例，用于从后端 API 获取健康监测数据
  factory HealthData.fromJson(Map<String, dynamic> json) {
    return HealthData(
      stepCount: json['stepCount']?.toInt() ?? 0,
      temperature: json['temperature']?.toDouble() ?? 37.0,
      datetime: _parseTimestamp(json['datetime']),
      calories: json['calories']?.toDouble() ?? 0.0,
      activityStatus: json['activityStatus'] != null
          ? (json['activityStatus'] as List<dynamic>)
              .map((item) => ActivityStatus.fromString(item.toString()))
              .toList()
          : [], // 处理空列表情况，或根据后端要求修改
      emotionStatus: json['emotionStatus'] != null
          ? EmotionStatus.fromString(json['emotionStatus'])
          : EmotionStatus.stable,
      healthDetections: json['healthDetections'] != null
          ? (json['healthDetections'] as List<dynamic>)
              .map((item) => HealthDetection.fromString(item.toString()))
              .toList()
          : [], // 处理空列表情况，或根据后端要求修改
    );
  }

  /// 将 HealthData 实例转换为 JSON 格式，用于将健康监测数据发送到后端 API
  Map<String, dynamic> toJson() {
    return {
      'stepCount': stepCount,
      'temperature': temperature,
      'datetime': datetime.toIso8601String(), // 或根据后端要求修改
      'calories': calories, // 或根据后端要求修改，或添加更多字段...
      'activityStatus': activityStatus
          .map((status) => status.displayName)
          .toList(), // 或根据后端要求修改，或添加更多字段...
      'emotionStatus': emotionStatus.displayName, // 或根据后端要求修改，或添加更多字段...
      'healthDetections': healthDetections
          .map((detection) => detection.displayName)
          .toList(), // 或根据后端要求修改，或添加更多字段...
    };
  }

  /// 辅助方法，解析时间戳
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp is String) {
      return DateTime.tryParse(timestamp)!.toLocal();
    } else if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else {
      return DateTime.now();
    }
  }

  static Future<void> saveHealthData(HealthData healthData,
      {String key = 'healthData'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    String jsonString = jsonEncode(healthData.toJson());
    await asyncPrefs.setString(storageKey, jsonString);
  }

  static Future<HealthData?> getHealthData({String key = 'healthData'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    String? jsonString = await asyncPrefs.getString(storageKey);
    if (jsonString != null) {
      return HealthData.fromJson(jsonDecode(jsonString));
    }
    return null;
  }
}
