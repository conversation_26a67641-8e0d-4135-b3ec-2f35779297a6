import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:custom_refresh_indicator/custom_refresh_indicator.dart';

import '../providers/health_provider.dart';
import '../providers/device_provider.dart';
import '../models/health_data.dart';
import '../widgets/health_card.dart';
import '../constants/activity_status.dart';
import '../constants/health_detection.dart';
import '../constants/constants.dart';

// 统计界面导入
import 'statistics/step_statistics_screen.dart';
import 'statistics/calorie_statistics_screen.dart';
import 'statistics/temperature_statistics_screen.dart';
import 'statistics/activity_status_statistics_screen.dart';
import 'statistics/emotion_status_statistics_screen.dart';
import 'statistics/health_detection_statistics_screen.dart';

class HealthScreen extends StatefulWidget {
  @override
  _HealthScreenState createState() => _HealthScreenState();
}

class _HealthScreenState extends State<HealthScreen> {
  final _indicatorController = IndicatorController();
  bool _isRefreshing = false;

  // 界面控制参数 - 控制四个新卡片的显示/隐藏,后续有VIP用户决定
  bool _showCalorieCard = true;
  bool _showActivityStatusCard = true;
  bool _showEmotionStatusCard = true;
  bool _showHealthDetectionCard = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchHealthData();
    });
  }

  Future<void> _fetchHealthData() async {
    if (_isRefreshing) return; // 防止重复刷新

    setState(() {
      _isRefreshing = true;
    });

    try {
      await Future.delayed(Duration(seconds: 2));
      final healthProvider =
          Provider.of<HealthProvider>(context, listen: false);
      final device = Provider.of<DeviceProvider>(context, listen: false).device;

      if (device != null) {
        await healthProvider.initHealthData();
        await healthProvider.fetchHealthData(device.deviceName);
      } else {
        // 如果设备为空，则提示用户没有设备
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('没有设备'),
              content: Text('请先添加设备才能查看健康数据'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('确定'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('刷新失败，请稍后重试'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final healthProvider = Provider.of<HealthProvider>(context);
    final data = healthProvider.healthData;

    return Scaffold(
      appBar: AppBar(
        title: Text('健康'),
        automaticallyImplyLeading: false,
      ),
      backgroundColor: Colors.grey[200],
      body: CustomRefreshIndicator(
        controller: _indicatorController,
        onRefresh: _fetchHealthData,
        builder: (context, child, controller) {
          return Stack(
            children: [
              child,
              if (controller.isLoading || _isRefreshing)
                Positioned(
                  top: 20,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            value:
                                controller.isLoading ? controller.value : null,
                          ),
                          SizedBox(height: 8),
                          Text(
                            '正在刷新...',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
        child: data == null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.health_and_safety,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 16),
                    Text(
                      '暂无健康数据',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '下拉刷新以获取最新数据',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              )
            : SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 第一排两张卡片：步数和体温
                    Row(
                      children: [
                        // 步数卡片
                        Expanded(
                          child: _buildStepCountCard(data),
                        ),
                        SizedBox(width: 16),
                        // 体温卡片
                        Expanded(
                          child: _buildTemperatureCard(data),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),

                    // 第二排两张卡片：卡路里和新的小卡片（如果需要）
                    Row(
                      children: [
                        // 卡路里卡片
                        if (_showCalorieCard)
                          Expanded(
                            child: _buildCalorieCard(data),
                          ),
                        if (_showCalorieCard) SizedBox(width: 16),
                        // 预留位置给其他小卡片
                        Expanded(
                          child: Container(), // 暂时空白
                        ),
                      ],
                    ),
                    if (_showCalorieCard) SizedBox(height: 16),
                    // 第四排：当前活动状态卡片
                    if (_showActivityStatusCard) _buildActivityStatusCard(data),
                    if (_showActivityStatusCard) SizedBox(height: 16),

                    // 第五排：当前情绪状态卡片
                    if (_showEmotionStatusCard) _buildEmotionStatusCard(data),
                    if (_showEmotionStatusCard) SizedBox(height: 16),

                    // 第六排：健康检测卡片
                    if (_showHealthDetectionCard)
                      _buildHealthDetectionCard(data),
                    if (_showHealthDetectionCard) SizedBox(height: 16),

                    // 第七排：健康分析报告卡片
                    _buildHealthReportCard(),
                    SizedBox(height: 16),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildTemperatureCard(data) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToTemperatureStatistics(),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '皮肤温度',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black54,
                ),
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.thermostat, size: 38, color: Colors.orange),
                  Text(
                    '${data.temperature}',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 4),
                  Text(
                    '°C',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(data.datetime),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepCountCard(data) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToStepStatistics(),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '步数',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black54,
                ),
              ),
              SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.pets, size: 38, color: Colors.orange),
                  Text(
                    '${data.stepCount}',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(width: 4),
                  Text(
                    '步',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(data.datetime),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建卡路里消耗卡片
  Widget _buildCalorieCard(HealthData data) {
    return CalorieHealthCard(
      calories: data.calories,
      timestamp: data.datetime,
      onTap: () => _navigateToCalorieStatistics(),
    );
  }

  /// 构建当前活动状态卡片
  Widget _buildActivityStatusCard(HealthData data) {
    // 如果活动状态列表为空，使用默认值
    final activityStatuses = data.activityStatus.isNotEmpty
        ? data.activityStatus
        : [ActivityStatus.stationary];

    // 如果只有一个活动状态，使用单状态卡片
    if (activityStatuses.length == 1) {
      final activityStatus = activityStatuses.first;
      final color =
          Color(int.parse('0xFF${activityStatus.colorHex.substring(1)}'));

      return ActivityStatusHealthCard(
        activityStatus: activityStatus.displayName,
        timestamp: data.datetime,
        statusColor: color,
        onTap: () => _navigateToActivityStatusStatistics(),
      );
    }

    // 如果有多个活动状态，使用多状态卡片
    final statusNames =
        activityStatuses.map((status) => status.displayName).toList();
    final statusColors = activityStatuses
        .map(
            (status) => Color(int.parse('0xFF${status.colorHex.substring(1)}')))
        .toList();

    return MultipleActivityStatusHealthCard(
      activityStatuses: statusNames,
      statusColors: statusColors,
      timestamp: data.datetime,
      onTap: () => _navigateToActivityStatusStatistics(),
    );
  }

  /// 构建当前情绪状态卡片
  Widget _buildEmotionStatusCard(HealthData data) {
    // 根据情绪状态获取颜色
    final color =
        Color(int.parse('0xFF${data.emotionStatus.colorHex.substring(1)}'));

    return EmotionStatusHealthCard(
      emotionStatus: data.emotionStatus.displayName,
      timestamp: data.datetime,
      statusColor: color,
      onTap: () => _navigateToEmotionStatusStatistics(),
    );
  }

  /// 构建健康检测卡片
  Widget _buildHealthDetectionCard(HealthData data) {
    // 检查是否有发热检测
    final hasFeverDetection = data.healthDetections
        .any((detection) => detection == HealthDetection.fever);

    return ColoredHealthDetectionCard(
      title: '当前异常健康监测',
      detections: data.healthDetections,
      timestamp: data.datetime,
      showDangerMark: hasFeverDetection,
      onTap: () => _navigateToHealthDetectionStatistics(),
    );
  }

  /// 构建健康分析报告卡片
  Widget _buildHealthReportCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToHealthReport(),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                Colors.blue.shade50,
                Colors.blue.shade100,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade200,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.analytics,
                  size: 32,
                  color: Colors.blue.shade700,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '宠物健康分析报告',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'AI智能分析宠物健康状况',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.blue.shade600,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 导航到步数统计界面
  void _navigateToStepStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StepStatisticsScreen(),
      ),
    );
  }

  /// 导航到卡路里统计界面
  void _navigateToCalorieStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CalorieStatisticsScreen(),
      ),
    );
  }

  /// 导航到体温统计界面
  void _navigateToTemperatureStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TemperatureStatisticsScreen(),
      ),
    );
  }

  /// 导航到活动状态统计界面
  void _navigateToActivityStatusStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ActivityStatusStatisticsScreen(),
      ),
    );
  }

  /// 导航到情绪状态统计界面
  void _navigateToEmotionStatusStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EmotionStatusStatisticsScreen(),
      ),
    );
  }

  /// 导航到健康检测统计界面
  void _navigateToHealthDetectionStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => HealthDetectionStatisticsScreen(),
      ),
    );
  }

  /// 导航到健康分析报告界面
  void _navigateToHealthReport() {
    Navigator.of(context).pushNamed(Routes.healthReport);
  }
}
