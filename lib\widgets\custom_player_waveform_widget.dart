import 'package:flutter/material.dart';

class CustomPlayerWaveformWidget extends StatelessWidget {
  final List<double> waveformData;
  final Duration currentTime;
  final Duration totalDuration;
  final double height;
  final Color playedColor;
  final Color unplayedColor;
  final Color centerLineColor;
  final double scaleFactor;

  const CustomPlayerWaveformWidget({
    Key? key,
    required this.waveformData,
    required this.currentTime,
    required this.totalDuration,
    this.height = 80,
    this.playedColor = Colors.red,
    this.unplayedColor = const Color(0xFFBBBBBB),
    this.centerLineColor = Colors.blue,
    this.scaleFactor = 70, // 与录音时保持一致
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: const Color(0xFFF5F5F5),
      ),
      padding: const EdgeInsets.only(left: 18, right: 18),
      child: CustomPaint(
        painter: PlayerWaveformPainter(
          waveformData: waveformData,
          currentTime: currentTime,
          totalDuration: totalDuration,
          playedColor: playedColor,
          unplayedColor: unplayedColor,
          centerLineColor: centerLineColor,
          scaleFactor: scaleFactor,
        ),
        size: Size.infinite,
      ),
    );
  }
}

class PlayerWaveformPainter extends CustomPainter {
  final List<double> waveformData;
  final Duration currentTime;
  final Duration totalDuration;
  final Color playedColor;
  final Color unplayedColor;
  final Color centerLineColor;
  final double scaleFactor;

  PlayerWaveformPainter({
    required this.waveformData,
    required this.currentTime,
    required this.totalDuration,
    required this.playedColor,
    required this.unplayedColor,
    required this.centerLineColor,
    required this.scaleFactor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (waveformData.isEmpty || totalDuration.inMilliseconds == 0) {
      // 如果没有波形数据，只绘制中心线
      _drawCenterLine(canvas, size);
      return;
    }

    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final strokeWidth = 1.5;
    final spacing = 2.0;
    final barTotalWidth = strokeWidth + spacing;

    // 计算播放进度 (0.0 到 1.0)
    final playProgress = totalDuration.inMilliseconds > 0
        ? currentTime.inMilliseconds / totalDuration.inMilliseconds
        : 0.0;

    // 计算左右两侧能显示多少个bar
    final leftBarCount = (centerX / barTotalWidth).floor();
    final rightBarCount = ((size.width - centerX) / barTotalWidth).floor();

    if (leftBarCount == 0 && rightBarCount == 0) return;

    // 绘制中心线
    _drawCenterLine(canvas, size);

    // 计算当前播放位置在整个波形数据中的索引
    final currentDataIndex = (waveformData.length * playProgress).floor();

    // 绘制左侧已播放波形（红色）
    _drawPlayedWaveform(canvas, size, centerX, centerY, strokeWidth,
        barTotalWidth, leftBarCount, currentDataIndex);

    // 绘制右侧未播放波形（灰色）
    _drawUnplayedWaveform(canvas, size, centerX, centerY, strokeWidth,
        barTotalWidth, rightBarCount, currentDataIndex);
  }

  void _drawCenterLine(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final centerLinePaint = Paint()
      ..color = centerLineColor
      ..strokeWidth = 1.0;
    canvas.drawLine(
      Offset(centerX, 0),
      Offset(centerX, size.height),
      centerLinePaint,
    );
  }

  void _drawPlayedWaveform(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double strokeWidth,
      double barTotalWidth,
      int leftBarCount,
      int currentDataIndex) {
    final playedPaint = Paint()
      ..color = playedColor
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    for (int i = 0; i < leftBarCount; i++) {
      // 从中心线向左，显示已播放的波形
      final dataIndex = currentDataIndex - i - 1;
      if (dataIndex < 0) break; // 如果超出数据范围就停止

      // final amplitude =
      //     waveformData[dataIndex % waveformData.length] * scaleFactor;
      final amplitude = waveformData[dataIndex] * scaleFactor;

      final x = centerX - (i + 1) * barTotalWidth;
      if (x <= 0) break;

      canvas.drawLine(
        Offset(x, centerY - amplitude),
        Offset(x, centerY + amplitude),
        playedPaint,
      );
    }
  }

  void _drawUnplayedWaveform(
      Canvas canvas,
      Size size,
      double centerX,
      double centerY,
      double strokeWidth,
      double barTotalWidth,
      int rightBarCount,
      int currentDataIndex) {
    final unplayedPaint = Paint()
      ..color = unplayedColor
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    for (int i = 0; i < rightBarCount; i++) {
      // 从中心线向右，显示未播放的波形
      final dataIndex = currentDataIndex + i;
      if (dataIndex >= waveformData.length) {
        break; // 如果超出数据范围就停止
      }

      // final amplitude = waveformData[dataIndex] * scaleFactor;
      final amplitude = waveformData[dataIndex] * scaleFactor;

      final x = centerX + (i + 1) * barTotalWidth;
      if (x >= size.width) {
        break;
      }

      canvas.drawLine(
        Offset(x, centerY - amplitude),
        Offset(x, centerY + amplitude),
        unplayedPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
