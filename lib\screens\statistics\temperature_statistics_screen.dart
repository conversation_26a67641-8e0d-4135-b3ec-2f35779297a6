import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 体温统计界面
class TemperatureStatisticsScreen extends StatefulWidget {
  @override
  _TemperatureStatisticsScreenState createState() =>
      _TemperatureStatisticsScreenState();
}

class _TemperatureStatisticsScreenState
    extends State<TemperatureStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  DateTime _currentDate = DateTime.now();
  Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false)
            .device
            ?.deviceName ??
        '';
    ChartService chartService =
        Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchTemperatureChartData(
              device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchTemperatureChartData(
              device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchTemperatureChartData(
              device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('皮肤温度统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表区域
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : Container(
                  width: double.infinity,
                  height: 350,
                  margin: EdgeInsets.symmetric(vertical: 16),
                  child: Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '温度变化趋势',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 16),
                          Expanded(
                            child: _buildLineChart(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
          // 统计信息
          // if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建曲线图
  Widget _buildLineChart() {
    if (_healthData['temperature']!.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    // 构建FlSpot列表
    List<FlSpot> spots = [];
    double k = 0;
    for (final data in _healthData['temperature']!) {
      spots.add(FlSpot(k, data.toDouble()));
      k++;
    }

    return LineChart(
      LineChartData(
        minY: 0,
        maxY: 40,
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: null,
          verticalInterval: null,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
              interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                  child: Text(
                    _getBottomTitle(value.toInt()),
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  space: 6, // 与绘图区的间距
                  child: Text(
                    value.toInt().toString(),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    textAlign: TextAlign.left,
                  ),
                );
              },
            ),
          ), // 显示右侧体温标题
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final index = barSpot.x.toInt();
                String timeRange = '';

                if (_selectedPeriod == TimePeriod.day) {
                  // 日模式显示时间段
                  final hour = index ~/ 4;
                  final minute = (index % 4) * 15;
                  final nextMinute = minute + 15;
                  if (nextMinute == 60) {
                    timeRange =
                        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                  } else {
                    timeRange =
                        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                  }
                } else if (_selectedPeriod == TimePeriod.month) {
                  // 月模式显示具体日期
                  final day = index + 1;
                  final month = _currentDate.month; // 使用当前月份
                  timeRange = '${month}月${day}日';
                } else {
                  // 周模式显示具体日期
                  DateTime monday = _currentDate
                      .subtract(Duration(days: _currentDate.weekday - 1));
                  final weekdays = List.generate(
                      7,
                      (index) => DateTime(
                            monday.year,
                            monday.month,
                            monday.day + index, // Dart 会自动处理日期溢出
                          ));
                  timeRange =
                      '${weekdays[index].month}月${weekdays[index].day}日';
                }

                return LineTooltipItem(
                  '$timeRange\n${barSpot.y.toStringAsFixed(1)}°C',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }).toList();
            },
          ),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true, // 使用光滑曲线
            curveSmoothness: 0.0, // 设置曲线平滑度，值越大越平滑
            color: _getTemperatureColor(),
            barWidth: 1.5,
            dotData: FlDotData(show: false), // 不显示曲线上的小圆点
            belowBarData: BarAreaData(
              show: true,
              color: _getTemperatureColor().withOpacity(0.1),
            ),
          ),
        ],
        // 添加正常体温范围的参考线  目前还没有验证测试不知道参考温度先注释掉这部分内容
        // extraLinesData: ExtraLinesData(
        //   horizontalLines: [
        //     HorizontalLine(
        //       y: 38.0, // 正常体温上限
        //       color: Colors.orange.withOpacity(0.5),
        //       strokeWidth: 2,
        //       dashArray: [5, 5],
        //     ),
        //     HorizontalLine(
        //       y: 36.0, // 正常体温下限
        //       color: Colors.green.withOpacity(0.5),
        //       strokeWidth: 2,
        //       dashArray: [5, 5],
        //     ),
        //   ],
        // ),
      ),
    );
  }

  /// 获取体温颜色
  Color _getTemperatureColor() {
    if (_healthData['temperature']!.isEmpty) return Colors.blue;

    final avgTemp =
        _healthData['temperature']!.fold(0, (sum, data) => sum + data) /
            _healthData['temperature']!.length;

    if (avgTemp >= 39.0) return Colors.red;
    if (avgTemp >= 38.5) return Colors.orange;
    if (avgTemp >= 37.5) return Colors.yellow.shade700;
    return Colors.green;
  }

  /// 获取底部标题间隔
  double _getBottomTitleInterval() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return 12.0; // 日模式：3小时间隔(12个15分钟)
      case TimePeriod.week:
        return 1.0; // 周模式：每天显示
      case TimePeriod.month:
        return 5.0; // 月模式：每5天显示一个标签
    }
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    List<String> weekdaysShort = [
      'Mon',
      'Tue',
      'Wed',
      'Thu',
      'Fri',
      'Sat',
      'Sun'
    ];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = index ~/ 4;
        final minute = (index % 4) * 15;
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return weekdaysShort[index];
      case TimePeriod.month:
        return '${index + 1}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final temperatures = _healthData['temperature']!;
    final avgTemp =
        temperatures.fold(0, (sum, temp) => sum + temp) / temperatures.length;
    final maxTemp = temperatures.reduce((a, b) => a > b ? a : b);
    final minTemp = temperatures.reduce((a, b) => a < b ? a : b);

    // 计算异常体温次数
    final abnormalCount =
        temperatures.where((temp) => temp > 38.0 || temp < 37.0).length;

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('平均体温', '${avgTemp.toStringAsFixed(1)}°C',
                      _getTemperatureColor()),
                  _buildStatItem(
                      '最高', '${maxTemp.toStringAsFixed(1)}°C', Colors.red),
                  _buildStatItem(
                      '最低', '${minTemp.toStringAsFixed(1)}°C', Colors.blue),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                      '异常次数', abnormalCount.toString(), Colors.orange),
                  _buildStatItem('正常范围', '37.0-38.0°C', Colors.green),
                  _buildStatItem(
                      '测量次数', temperatures.length.toString(), Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
