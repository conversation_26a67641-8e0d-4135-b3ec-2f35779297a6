/// 健康监测异常状态枚举
/// 定义宠物的各种健康监测异常状态类型
enum HealthDetection {
  /// 发热
  fever('发热/热射病'),

  /// 呼吸异常
  breathingAbnormal('呼吸异常'),

  /// 运动不稳
  movementUnstable('运动不平稳'),

  /// 步态不对称
  gaitAsymmetry('步态不对称'),

  /// 步态规律性下降
  gaitRegularityDecrease('步态规律性下降'),

  /// 夜醒
  nightWaking('夜醒'),

  /// 睡眠不足
  sleepInsufficiency('睡眠不足'),

  /// 运动复杂度异常
  movementComplexityAbnormal('运动复杂度异常'),

  /// 活动模式改变
  activityPatternChange('活动模式改变'),

  /// 卡路里下降
  calorieDecrease('kcal下降');

  const HealthDetection(this.displayName);

  /// 显示名称
  final String displayName;

  /// 从字符串获取健康监测状态
  static HealthDetection fromString(String value) {
    for (HealthDetection detection in HealthDetection.values) {
      if (detection.displayName == value || detection.name == value) {
        return detection;
      }
    }
    throw ArgumentError('未知的健康监测状态: $value');
  }

  /// 从字符串列表获取健康监测状态列表
  static List<HealthDetection> fromStringList(List<String> values) {
    return values.map((value) => fromString(value)).toList();
  }

  /// 获取所有健康监测状态的显示名称列表
  static List<String> get allDisplayNames {
    return HealthDetection.values
        .map((detection) => detection.displayName)
        .toList();
  }

  /// 获取健康监测状态对应的颜色
  String get colorHex {
    switch (this) {
      case HealthDetection.fever:
        return '#F44336'; // 红色 - 高危
      case HealthDetection.breathingAbnormal:
        return '#E91E63'; // 粉红色 - 高危
      case HealthDetection.movementUnstable:
        return '#FF5722'; // 深橙色 - 中高危
      case HealthDetection.gaitAsymmetry:
        return '#FF9800'; // 橙色 - 中危
      case HealthDetection.gaitRegularityDecrease:
        return '#FFC107'; // 黄色 - 中危
      case HealthDetection.nightWaking:
        return '#9C27B0'; // 紫色 - 中危
      case HealthDetection.sleepInsufficiency:
        return '#673AB7'; // 深紫色 - 中危
      case HealthDetection.movementComplexityAbnormal:
        return '#3F51B5'; // 靛蓝色 - 低中危
      case HealthDetection.activityPatternChange:
        return '#4CAF50'; // 绿色 - 低危
      case HealthDetection.calorieDecrease:
        return '#2196F3'; // 蓝色 - 低危
    }
  }

  /// 获取健康监测状态量化成数字
  int get severityLevel {
    switch (this) {
      case HealthDetection.fever:
        return 10; // 最严重
      case HealthDetection.breathingAbnormal:
        return 9; // 最严重
      case HealthDetection.gaitAsymmetry:
        return 8;
      case HealthDetection.movementUnstable:
        return 7;
      case HealthDetection.calorieDecrease:
        return 6;
      case HealthDetection.nightWaking:
        return 5;
      case HealthDetection.sleepInsufficiency:
        return 4;
      case HealthDetection.gaitRegularityDecrease:
        return 3;
      case HealthDetection.movementComplexityAbnormal:
        return 2;
      case HealthDetection.activityPatternChange:
        return 1; // 最轻微
    }
  }

  /// 判断是否为高危状态
  bool get isHighRisk {
    return severityLevel >= 4;
  }

  /// 判断是否为中危状态
  bool get isMediumRisk {
    return severityLevel == 3;
  }

  /// 判断是否为低危状态
  bool get isLowRisk {
    return severityLevel <= 2;
  }

  /// 判断是否需要显示"危"字标记
  bool get needsDangerMark {
    return this == HealthDetection.fever;
  }

  /// 判断是否为危险状态（与 needsDangerMark 相同，用于兼容性）
  bool get isDangerous {
    return needsDangerMark;
  }
}
