import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/pet.dart';
import '../constants/constants.dart';
import '../widgets/pet_info_card.dart';
import '../widgets/no_pet_card.dart';
import '../widgets/pet_loading_card.dart';
import '../providers/auth_provider.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面重新获得焦点时刷新宠物数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.refreshPetInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
        elevation: 1,
      ),
      backgroundColor: Colors.grey[200],
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.centerLeft,
              child: Text(
                '爱宠信息',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return _buildPetCard();
              },
            ),
            const SizedBox(height: 24),
            _buildMenuSection(),
            const SizedBox(height: 24),
            _buildLogoutButton(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildPetCard() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (authProvider.isPetLoading) {
      return const PetLoadingCard();
    }

    if (authProvider.currentPet == null) {
      return NoPetCard(onTap: _navigateToAddPet);
    } else {
      return PetInfoCard(
        pet: authProvider.currentPet!,
        onTap: _navigateToPetDetail,
      );
    }
  }

  void _navigateToAddPet() async {
    final result = await Navigator.of(context).pushNamed(Routes.petInfoInput);
    if (result != null) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.refreshPetInfo(); // 重新加载宠物信息
    }
  }

  void _navigateToPetDetail() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final result = await Navigator.of(context)
        .pushNamed(Routes.petDetail, arguments: authProvider.currentPet);
    if (result != null) {
      await authProvider.refreshPetInfo(); // 重新加载宠物信息
    }
  }

  // 构建菜单部分
  Widget _buildMenuSection() {
    final menuItems = [
      {
        'icon': Icons.person,
        'title': '个人资料',
        'onTap': () {
          // TODO: 导航到个人资料页面
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('个人资料功能开发中')),
          );
        },
      },
      {
        'icon': Icons.link_off,
        'title': '项圈解绑',
        'onTap': () {
          // TODO: 导航到项圈解绑页面
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('项圈解绑功能开发中')),
          );
        },
      },
      {
        'icon': Icons.help_outline,
        'title': '帮助与客服',
        'onTap': () {
          // TODO: 导航到帮助与客服页面
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('帮助与客服功能开发中')),
          );
        },
      },
      {
        'icon': Icons.info_outline,
        'title': '关于',
        'onTap': () {
          // TODO: 导航到关于页面
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('关于功能开发中')),
          );
        },
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: menuItems.length,
          separatorBuilder: (context, index) => Divider(
            height: 1,
            color: Colors.grey[200],
            indent: 64,
            endIndent: 16,
          ),
          itemBuilder: (context, index) {
            final item = menuItems[index];
            return ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 20),
              leading: Icon(
                item['icon'] as IconData,
                color: Colors.grey[600],
                size: 24,
              ),
              title: Text(
                item['title'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 14,
              ),
              onTap: item['onTap'] as VoidCallback,
            );
          },
        ),
      ),
    );
  }

  // 构建退出登录按钮
  Widget _buildLogoutButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () => _showLogoutConfirmDialog(),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orangeAccent,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            elevation: 2,
          ),
          child: const Text(
            '退出登录',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  // 显示退出登录确认对话框
  void _showLogoutConfirmDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认退出'),
          content: const Text('您确定要退出登录吗？'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(
                  color: Colors.grey[600],
                ),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _logout();
              },
              child: Text(
                '确认',
                style: TextStyle(
                  color: Colors.red[400],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // 执行退出登录
  Future<void> _logout() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.logout();

      // 跳转到登录页面
      Navigator.of(context).pushNamedAndRemoveUntil(
        Routes.login,
        (route) => false,
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('退出登录失败: $e'),
          backgroundColor: Colors.red[400],
        ),
      );
    }
  }
}
