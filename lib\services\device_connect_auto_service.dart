import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:wifi_scan/wifi_scan.dart';
import 'package:app_settings/app_settings.dart';
import 'package:blufi_package/blufi_package.dart';

import '../models/device.dart';
import '../models/wifi.dart';
import '../services/wifi_scan_service.dart';
import '../services/vibration_service.dart';
import '../services/device_service.dart';
import '../utils/app_logger.dart';
import '../constants/constants.dart';

/// 自动配网状态枚举
enum AutoConnectState {
  idle, // 空闲状态
  detecting, // 检测状态
  hotspotConfig, // 热点配网状态
  homeWifiConfig, // 家庭WiFi配网状态
  antilosting, // 防丢模式状态
}

/// 自动配网服务
/// 负责监听设备状态变化，自动进行热点配网和家庭WiFi配网
class DeviceConnectAutoService {
  final BluFiService _bluFiService = BluFiService();
  final WifiScanService _wifiScanService = WifiScanService();
  final VibrationService _vibrationService = VibrationService();
  final DeviceApiService _deviceApiService;

  // 状态变量
  AutoConnectState _currentState = AutoConnectState.idle;
  Timer? _homeWifiMonitorTimer;
  bool _isHomemoniting = false;
  bool _isWifiUpdate = false;
  bool _isprocessing = false;
  String? _deviceBleMac;
  String? _deviceName;
  wifiConfig? _wifiComfig;

  // 连接信息对话框相关
  BuildContext? _connectionDialogContext;

  // 获取当前状态
  AutoConnectState get currentState => _currentState;

  /// 构造函数
  DeviceConnectAutoService(this._deviceApiService);

  /// 开始监听设备状态
  void startMonitoring(Device device) {
    if (_currentState != AutoConnectState.idle) return;

    _deviceBleMac = device.bleMac;
    _deviceName = device.deviceName;
    _updateCurrentState(AutoConnectState.detecting);
    AppLogger.info('开始监听设备状态: ${device.deviceName}');
    _handleDeviceOffline();
  }

  /// 清除防丢模式
  void clearAntiLostState() {
    if (_currentState == AutoConnectState.antilosting) {
      _currentState = AutoConnectState.idle;
    }
  }

  /// 停止监听
  void stopMonitoring() {
    // _cancelTimers();
    _updateCurrentState(AutoConnectState.idle);
    AppLogger.info('停止监听设备状态');
  }

  /// 处理设备离线事件
  Future<void> _handleDeviceOffline() async {
    _wifiComfig = await wifiConfig.getConfig();
    // 触发设备振动
    await _vibrationService.vibrate();
    // 显示场景选择对话框
    _showSceneSelectionDialog();
  }

  /// 选择外出遛狗场景
  void selectOutdoorWalkingScene() {
    if (_currentState != AutoConnectState.detecting) return;
    // 跳转到系统的热点设置界面
    _openHotspotSettings();
  }

  /// 选择宠物走丢场景
  void selectPetLostScene() {
    if (_currentState != AutoConnectState.detecting) return;
    // 触发走丢提醒功能（这里只是重置状态，实际走丢提醒由其他组件处理）
    _updateCurrentState(AutoConnectState.antilosting);
  }

  /// 开始热点配网流程
  Future<void> _startHotspotConfigProcess() async {
    AppLogger.info('开始热点配网流程');

    _updateCurrentState(AutoConnectState.hotspotConfig);

    int checkCount = 0;
    bool flag = false;
    while (checkCount < 3) {
      checkCount++;

      // 获取热点配置
      if (_wifiComfig != null && !flag) {
        // 开始蓝牙配网
        flag = true;
        await _startBleConfig2(
            _wifiComfig!.hotspotSsid, _wifiComfig!.hotspotPassword);
        flag = false;
      }
      await Future.delayed(Duration(seconds: 3));
      if (_currentState != AutoConnectState.hotspotConfig) {
        break;
      }
    }
    //仍未检测到热点则退出
    if (_currentState == AutoConnectState.hotspotConfig) {
      _handleConfigComplete(false, '连接热点失败！请确认热点已经开启且密码正确！\n请确认项圈在手机一米范围内！');
      // _resetState();
    }
  }

  ///使用BLUFI_SERVICE开始进行蓝牙配网
  Future<void> _startBleConfig2(String ssid, String password) async {
    if (_isprocessing) {
      return;
    }

    _isprocessing = true;
    await _bluFiService.initialize();

    await _bluFiService.startScan();
    await Future.delayed(Duration(seconds: 10));

    if (_bluFiService.isDeviceExist(_deviceBleMac!)) {
      await _bluFiService.connectToDevice(_deviceBleMac!);
    } else {
      _isprocessing = false;
      return;
    }

    if (_bluFiService.state != BluFiServiceState.ready) {
      _isprocessing = false;
      return;
    }

    await _bluFiService.getWiFiStatus();

    if (_bluFiService.state == BluFiServiceState.idle &&
        _currentState == AutoConnectState.hotspotConfig) {
      _isprocessing = false;
      // 热点配网结束进入到家庭WIFI配网
      _handleConfigComplete(true, '连接热点成功');
      _startHomeWifiMonitoring();
      return;
    }

    bool config = await _bluFiService.configureWiFi(ssid, password);

    await Future.delayed(Duration(seconds: 3));
    if (_bluFiService.state == BluFiServiceState.idle) {
      if (_currentState == AutoConnectState.homeWifiConfig) {
        _isHomemoniting = false;
        //结束自动配网
        _cancelTimers();
        _awaitResetState();
      } else if (_currentState == AutoConnectState.hotspotConfig) {
        // 热点配网结束进入到家庭WIFI配网
        _handleConfigComplete(true, '连接热点成功');
        _startHomeWifiMonitoring();
      }
    } else if (config && _bluFiService.state == BluFiServiceState.ready) {
      //配网成功但是热点没有打开，或者是密码错误
      AppLogger.warning('热点网络未打开或者密码错误');
      _bluFiService.disconnectBle();
    } else {
      _bluFiService.disconnectBle();
    }
    if (_currentState == AutoConnectState.homeWifiConfig) {
      _startHomeWifiMonitoring();
    }
    _isprocessing = false;
  }

  /// 启动家庭WiFi监测
  void _startHomeWifiMonitoring() {
    if (_isHomemoniting) {
      return;
    }

    AppLogger.info('开始监测家庭WiFi信号');
    _isHomemoniting = true;
    // 切换到家庭WiFi配网状态
    _updateCurrentState(AutoConnectState.homeWifiConfig);

    try {
      // 取消之前的定时器
      _homeWifiMonitorTimer?.cancel();
      // 每30秒检测一次家庭WiFi信号
      _homeWifiMonitorTimer = Timer.periodic(
        const Duration(seconds: 60),
        (timer) async {
          // 扫描WiFi网络
          final accessPoints = await _wifiScanService.scanWifiNetworks();

          // 查找家庭WiFi
          WiFiAccessPoint? homeWifi;
          try {
            homeWifi = accessPoints.firstWhere(
              (ap) => ap.ssid == _wifiComfig!.homeSsid,
            );
          } catch (e) {
            // 未找到家庭WiFi
            homeWifi = null;
          }

          // 检查信号强度是否足够
          if (homeWifi != null &&
              homeWifi.ssid.isNotEmpty &&
              homeWifi.level > -60) {
            AppLogger.info(
                '检测到家庭WiFi信号强度足够: ${homeWifi.level} dBm,开始切换到家庭WiFi');
            timer.cancel();
            _isHomemoniting = false;
            _startHomeWifiConfigProcess(
                _wifiComfig!.homeSsid, _wifiComfig!.homePassword);
          }
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('监测家庭WiFi信号失败: $e', error: e, stackTrace: stackTrace);
      _isHomemoniting = false;
    }
  }

  /// 开始家庭WiFi配网流程
  Future<void> _startHomeWifiConfigProcess(String ssid, String password) async {
    AppLogger.info('开始家庭WiFi配网流程');

    try {
      // 发送WIFI_UPDATE指令断开当前WiFi连接
      if (!_isWifiUpdate) {
        final success =
            await _deviceApiService.deviceWifiConfigUpdate(_deviceName!);
        if (!success) {
          AppLogger.warning('发送WiFi更新指令失败');
          _isHomemoniting = false;
          _startHomeWifiMonitoring();
          return;
        } else {
          //上次已经恢复WIFI初始状态但是blufi配网失败了
          _isWifiUpdate = true;
        }
        // 等待设备断开WiFi连接
        await Future.delayed(const Duration(seconds: 2));
      }
      // 开始蓝牙配网
      _startBleConfig2(ssid, password);
    } catch (e, stackTrace) {
      AppLogger.error('家庭WiFi配网失败: $e', error: e, stackTrace: stackTrace);
      _startHomeWifiMonitoring();
    } finally {
      _isHomemoniting = false;
    }
  }

  ///延迟重置状态，等到服务器更新项圈网络状态
  Future<void> _awaitResetState() async {
    _updateCurrentState(AutoConnectState.detecting);
    //等待30秒,等待服务器更新项圈网络状态
    await Future.delayed(Duration(seconds: 30));
    _resetState();
  }

  void _updateCurrentState(AutoConnectState state) {
    if (_currentState != state) {
      AppLogger.info('自动连接状态更新: ${_currentState} -> $state');
      _currentState = state;
    }
  }

  /// 重置状态
  void _resetState() {
    // _cancelTimers();
    _updateCurrentState(AutoConnectState.idle);
  }

  /// 取消所有定时器
  void _cancelTimers() {
    _homeWifiMonitorTimer?.cancel();
  }

  // 释放资源
  void dispose() {
    _cancelTimers();
  }

  /// 内部处理配网完成事件
  void _handleConfigComplete(bool success, String message) {
    // 更新连接信息对话框
    _updateConnectionDialog(message, showConfirmButton: !success);
  }

  /// 显示连接信息对话框
  void _showConnectionInfoDialog() {
    final globalContext = navigatorKey.currentContext;
    if (globalContext == null) return;

    showDialog(
      context: globalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        _connectionDialogContext = context;
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('连接状态'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text('正在连接热点中......'),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 更新连接信息对话框内容
  void _updateConnectionDialog(String message,
      {bool showConfirmButton = false}) {
    if (_connectionDialogContext == null) return;

    final context = _connectionDialogContext!;
    if (!context.mounted) return;

    Navigator.of(context).pop();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        _connectionDialogContext = dialogContext; // 更新对话框上下文
        return AlertDialog(
          title: const Text('连接状态'),
          content: Text(message),
          actions: showConfirmButton
              ? <Widget>[
                  TextButton(
                    child: const Text('退出'),
                    onPressed: () {
                      _updateCurrentState(AutoConnectState.antilosting);
                      Navigator.of(dialogContext).pop();
                      _connectionDialogContext = null;
                    },
                  ),
                  TextButton(
                    child: const Text('重试'),
                    onPressed: () {
                      Navigator.of(dialogContext).pop();
                      _connectionDialogContext = null;
                      _openHotspotSettings();
                    },
                  ),
                ]
              : null,
        );
      },
    );

    // 如果不需要确认按钮，自动关闭对话框
    if (!showConfirmButton) {
      Timer(const Duration(seconds: 1), () {
        if (_connectionDialogContext != null &&
            _connectionDialogContext!.mounted) {
          Navigator.of(_connectionDialogContext!).pop();
          _connectionDialogContext = null;
        }
      });
    }
  }

  /// 显示场景选择对话框
  void _showSceneSelectionDialog() {
    final globalContext = navigatorKey.currentContext;
    if (globalContext == null) return;
    showDialog(
      context: globalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('项圈已离线'),
          content: const Text(
              '检测到项圈WiFi断开,请确认您的宠物状态:\n 1、确认家用Wifi网络是否正常,若没有问题,您的宠物可能已经出逃。\n 2、若是带宠物出门遛弯,请开启热点,项圈将尝试连接手机热点获得网络'),
          actions: <Widget>[
            TextButton(
              child: const Text('开启热点'),
              onPressed: () async {
                await _vibrationService.stopVibration();
                Navigator.of(context).pop();
                selectOutdoorWalkingScene();
              },
            ),
            TextButton(
              child: const Text('取消'),
              onPressed: () async {
                await _vibrationService.stopVibration();
                Navigator.of(context).pop();
                selectPetLostScene();
              },
            ),
          ],
        );
      },
    );
  }

  /// 跳转到系统热点设置界面
  Future<void> _openHotspotSettings() async {
    try {
      AppLogger.info('准备跳转到系统热点设置界面');

      // 根据平台跳转到相应的设置界面
      if (Platform.isAndroid) {
        // Android: 跳转到热点设置界面
        await AppSettings.openAppSettings(type: AppSettingsType.hotspot);
        AppLogger.info('已跳转到Android热点设置界面');
      } else if (Platform.isIOS) {
        // iOS: 跳转到WiFi设置界面（iOS不支持直接跳转到热点设置）
        await AppSettings.openAppSettings(type: AppSettingsType.wifi);
        AppLogger.info('已跳转到iOS WiFi设置界面');
      } else {
        // 其他平台：跳转到应用设置
        await AppSettings.openAppSettings();
        AppLogger.info('已跳转到应用设置界面');
      }

      // 显示确认对话框，让用户确认已开启热点
      _showHotspotConfirmDialog();
    } catch (e, stackTrace) {
      AppLogger.error('跳转到热点设置界面失败: $e', error: e, stackTrace: stackTrace);

      // 如果跳转失败，显示提示信息并直接显示确认对话框
      _showHotspotConfirmDialog();
    }
  }

  /// 格式化密码显示（前3位 + *** + 后3位）
  String _formatPasswordForDisplay(String password) {
    if (password.length <= 6) {
      return '***';
    }
    return '${password.substring(0, 3)}***${password.substring(password.length - 3)}';
  }

  /// 显示热点确认对话框
  void _showHotspotConfirmDialog() {
    final globalContext = navigatorKey.currentContext;
    if (globalContext == null) return;

    final formattedPassword =
        _formatPasswordForDisplay(_wifiComfig!.hotspotPassword);

    showDialog(
      context: globalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('连接热点'),
          content: Text('热点名称: ${_wifiComfig!.hotspotSsid}\n'
              '热点密码: $formattedPassword\n'
              '请确认您热点信息是否发生更改，如发生更改请更新热点信息。'),
          actions: <Widget>[
            TextButton(
              child: const Text('更新热点'),
              onPressed: () {
                Navigator.of(context).pop();
                _showUpdateHotspotInfoDialog();
              },
            ),
            TextButton(
              child: const Text('开始连接'),
              onPressed: () {
                Navigator.of(context).pop();
                // 立即显示连接信息对话框
                _showConnectionInfoDialog();
                // 用户确认已开启热点，开始自动连接热点流程
                _startHotspotConfigProcess();
              },
            ),
          ],
        );
      },
    );
  }

  /// 显示更新热点信息对话框
  void _showUpdateHotspotInfoDialog() {
    final globalContext = navigatorKey.currentContext;
    if (globalContext == null) return;

    final TextEditingController ssidController =
        TextEditingController(text: _wifiComfig!.hotspotSsid);
    final TextEditingController passwordController =
        TextEditingController(text: _wifiComfig!.hotspotPassword);

    showDialog(
      context: globalContext,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('更新热点信息'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: ssidController,
                decoration: const InputDecoration(
                  labelText: '热点名称',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: passwordController,
                decoration: const InputDecoration(
                  labelText: '热点密码',
                  border: OutlineInputBorder(),
                ),
                obscureText: false,
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
                // 返回上一级对话框
                _showHotspotConfirmDialog();
              },
            ),
            TextButton(
              child: const Text('保存并连接'),
              onPressed: () async {
                final newSsid = ssidController.text.trim();
                final newPassword = passwordController.text.trim();

                // 验证输入
                if (newSsid.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('热点名称不能为空')),
                  );
                  return;
                }

                if (newPassword.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('热点密码不能为空')),
                  );
                  return;
                }

                // 创建新的配置对象
                final updatedConfig = wifiConfig(
                  homeSsid: _wifiComfig!.homeSsid,
                  homePassword: _wifiComfig!.homePassword,
                  hotspotSsid: newSsid,
                  hotspotPassword: newPassword,
                );

                // 保存到本地存储
                await wifiConfig.saveConfig(updatedConfig);

                // 更新当前配置引用
                _wifiComfig = updatedConfig;

                Navigator.of(context).pop();

                // 显示连接信息对话框并开始连接流程
                _showConnectionInfoDialog();
                _startHotspotConfigProcess();
              },
            ),
          ],
        );
      },
    );
  }
}
