import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 时间段类型枚举
enum TimePeriod {
  day('日'),
  week('周'),
  month('月');

  const TimePeriod(this.displayName);
  final String displayName;
}

/// 时间段选择组件
class TimePeriodSelector extends StatelessWidget {
  final TimePeriod selectedPeriod;
  final Function(TimePeriod) onPeriodChanged;
  final DateTime currentDate;

  const TimePeriodSelector({
    Key? key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
    required this.currentDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 时间段选择按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: TimePeriod.values.map((period) {
              final isSelected = period == selectedPeriod;
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 4),
                child: GestureDetector(
                  onTap: () => onPeriodChanged(period),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      period.displayName,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey.shade600,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 12),
          // 时间显示
          Text(
            _getTimeDisplayText(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取时间显示文本
  String _getTimeDisplayText() {
    switch (selectedPeriod) {
      case TimePeriod.day:
        return _getDayDisplayText();
      case TimePeriod.week:
        return _getWeekDisplayText();
      case TimePeriod.month:
        return _getMonthDisplayText();
    }
  }

  /// 获取日视图显示文本
  String _getDayDisplayText() {
    final formatter = DateFormat('yyyy年MM月dd日');
    final weekdayFormatter = DateFormat('EEEE', 'zh_CN');
    return '${formatter.format(currentDate)} ${weekdayFormatter.format(currentDate)}';
  }

  /// 获取周视图显示文本
  String _getWeekDisplayText() {
    // 计算本周的开始和结束日期（周一到周日）
    final weekday = currentDate.weekday;
    final startOfWeek = currentDate.subtract(Duration(days: weekday - 1));
    final endOfWeek = startOfWeek.add(Duration(days: 6));

    // 如果跨年，显示完整年份
    if (startOfWeek.year != endOfWeek.year) {
      final startFormatter = DateFormat('yyyy年MM月dd日');
      final endFormatter = DateFormat('yyyy年MM月dd日');
      return '${startFormatter.format(startOfWeek)} - ${endFormatter.format(endOfWeek)}';
    } else {
      // 同一年，显示年份
      final yearFormatter = DateFormat('yyyy年');
      final dateFormatter = DateFormat('MM月dd日');
      return '${yearFormatter.format(startOfWeek)}${dateFormatter.format(startOfWeek)} - ${dateFormatter.format(endOfWeek)}';
    }
  }

  /// 获取月视图显示文本
  String _getMonthDisplayText() {
    final startOfMonth = DateTime(currentDate.year, currentDate.month, 1);
    final endOfMonth = DateTime(currentDate.year, currentDate.month + 1, 0);

    final yearFormatter = DateFormat('yyyy年');
    final dateFormatter = DateFormat('MM月dd日');
    return '${yearFormatter.format(startOfMonth)}${dateFormatter.format(startOfMonth)} - ${dateFormatter.format(endOfMonth)}';
  }
}

/// 时间段选择器的扩展版本，包含导航功能
class TimePeriodSelectorWithNavigation extends StatelessWidget {
  final TimePeriod selectedPeriod;
  final Function(TimePeriod) onPeriodChanged;
  final DateTime currentDate;
  final Function(DateTime) onDateChanged;

  const TimePeriodSelectorWithNavigation({
    Key? key,
    required this.selectedPeriod,
    required this.onPeriodChanged,
    required this.currentDate,
    required this.onDateChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 增加顶部间距
          SizedBox(height: 16),
          // 时间段选择按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: TimePeriod.values.map((period) {
              final isSelected = period == selectedPeriod;
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 4),
                child: GestureDetector(
                  onTap: () => _handlePeriodChange(period),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      period.displayName,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey.shade600,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 12),
          // 时间导航和显示
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () => _navigatePrevious(),
                icon: Icon(Icons.chevron_left, color: Colors.grey.shade600),
              ),
              Expanded(
                child: Center(
                  child: GestureDetector(
                    onTap: () => _showDatePicker(context),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            _getTimeDisplayText(),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade700,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              IconButton(
                onPressed: _canNavigateNext() ? () => _navigateNext() : null,
                icon: Icon(
                  Icons.chevron_right,
                  color: _canNavigateNext()
                      ? Colors.grey.shade600
                      : Colors.grey.shade300,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 处理时间模式切换
  void _handlePeriodChange(TimePeriod newPeriod) {
    // 如果切换到日模式，需要确保当前日期不超过今天
    if (newPeriod == TimePeriod.day) {
      final today = DateTime.now();
      final todayOnly = DateTime(today.year, today.month, today.day);
      final currentDateOnly =
          DateTime(currentDate.year, currentDate.month, currentDate.day);

      if (currentDateOnly.isAfter(todayOnly)) {
        // 如果当前日期超过今天，则设置为今天
        onDateChanged(todayOnly);
      }
    }

    // 调用原始的回调
    onPeriodChanged(newPeriod);
  }

  /// 显示日期选择器
  void _showDatePicker(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(), // 限制不能选择未来日期
      locale: const Locale('zh', 'CN'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).primaryColor,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != currentDate) {
      onDateChanged(picked);
    }
  }

  /// 导航到上一个时间段
  void _navigatePrevious() {
    DateTime newDate;
    switch (selectedPeriod) {
      case TimePeriod.day:
        newDate = currentDate.subtract(Duration(days: 1));
        break;
      case TimePeriod.week:
        newDate = currentDate.subtract(Duration(days: 7));
        break;
      case TimePeriod.month:
        newDate =
            DateTime(currentDate.year, currentDate.month - 1, currentDate.day);
        break;
    }
    onDateChanged(newDate);
  }

  /// 导航到下一个时间段
  void _navigateNext() {
    // 先检查是否可以导航
    if (!_canNavigateNext()) {
      return;
    }

    DateTime newDate;
    switch (selectedPeriod) {
      case TimePeriod.day:
        newDate = currentDate.add(Duration(days: 1));
        break;
      case TimePeriod.week:
        newDate = currentDate.add(Duration(days: 7));
        break;
      case TimePeriod.month:
        newDate =
            DateTime(currentDate.year, currentDate.month + 1, currentDate.day);
        break;
    }

    onDateChanged(newDate);
  }

  /// 获取时间显示文本
  String _getTimeDisplayText() {
    switch (selectedPeriod) {
      case TimePeriod.day:
        return _getDayDisplayText();
      case TimePeriod.week:
        return _getWeekDisplayText();
      case TimePeriod.month:
        return _getMonthDisplayText();
    }
  }

  /// 获取日视图显示文本
  String _getDayDisplayText() {
    final formatter = DateFormat('yyyy年MM月dd日');
    final weekdayFormatter = DateFormat('EEEE', 'zh_CN');
    return '${formatter.format(currentDate)} ${weekdayFormatter.format(currentDate)}';
  }

  /// 获取周视图显示文本
  String _getWeekDisplayText() {
    final weekday = currentDate.weekday;
    final startOfWeek = currentDate.subtract(Duration(days: weekday - 1));
    final endOfWeek = startOfWeek.add(Duration(days: 6));

    // 如果跨年，显示完整年份
    if (startOfWeek.year != endOfWeek.year) {
      final startFormatter = DateFormat('yyyy年MM月dd日');
      final endFormatter = DateFormat('yyyy年MM月dd日');
      return '${startFormatter.format(startOfWeek)} - ${endFormatter.format(endOfWeek)}';
    } else {
      // 同一年，显示年份
      final yearFormatter = DateFormat('yyyy年');
      final dateFormatter = DateFormat('MM月dd日');
      return '${yearFormatter.format(startOfWeek)}${dateFormatter.format(startOfWeek)} - ${dateFormatter.format(endOfWeek)}';
    }
  }

  /// 获取月视图显示文本
  String _getMonthDisplayText() {
    final startOfMonth = DateTime(currentDate.year, currentDate.month, 1);
    final endOfMonth = DateTime(currentDate.year, currentDate.month + 1, 0);

    final yearFormatter = DateFormat('yyyy年');
    final dateFormatter = DateFormat('MM月dd日');
    return '${yearFormatter.format(startOfMonth)}${dateFormatter.format(startOfMonth)} - ${dateFormatter.format(endOfMonth)}';
  }

  /// 检查是否可以导航到下一个时间段
  bool _canNavigateNext() {
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);

    switch (selectedPeriod) {
      case TimePeriod.day:
        // 日模式：检查当前日期是否已经是今天
        final currentDateOnly =
            DateTime(currentDate.year, currentDate.month, currentDate.day);
        return currentDateOnly.isBefore(todayOnly);

      case TimePeriod.week:
        // 周模式：检查当前周是否包含今天
        final weekday = currentDate.weekday;
        final startOfWeek = currentDate.subtract(Duration(days: weekday - 1));
        final endOfWeek = startOfWeek.add(Duration(days: 6));
        final endOfWeekOnly =
            DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day);
        return endOfWeekOnly.isBefore(todayOnly);

      case TimePeriod.month:
        // 月模式：检查当前月是否包含今天
        final endOfMonth = DateTime(currentDate.year, currentDate.month + 1, 0);
        final endOfMonthOnly =
            DateTime(endOfMonth.year, endOfMonth.month, endOfMonth.day);
        return endOfMonthOnly.isBefore(todayOnly);
    }
  }
}
