import 'package:flutter/material.dart';

/// 气泡状态显示组件
/// 用于显示活动状态和情绪状态等信息
class BubbleStatusDisplay extends StatelessWidget {
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const BubbleStatusDisplay({
    Key? key,
    required this.text,
    this.backgroundColor,
    this.textColor,
    this.fontSize,
    this.padding,
    this.borderRadius,
  }) : super(key: key);

  /// 创建活动状态气泡
  factory BubbleStatusDisplay.activity({
    required String text,
    Color? customColor,
  }) {
    return BubbleStatusDisplay(
      text: text,
      backgroundColor: customColor ?? Colors.blue.shade100,
      textColor: customColor != null ? Colors.white : Colors.blue.shade800,
      fontSize: 14,
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      borderRadius: BorderRadius.circular(16),
    );
  }

  /// 创建情绪状态气泡
  factory BubbleStatusDisplay.emotion({
    required String text,
    Color? customColor,
  }) {
    return BubbleStatusDisplay(
      text: text,
      backgroundColor: customColor ?? Colors.green.shade100,
      textColor: customColor != null ? Colors.white : Colors.green.shade800,
      fontSize: 14,
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      borderRadius: BorderRadius.circular(16),
    );
  }

  /// 创建健康检测气泡
  factory BubbleStatusDisplay.health({
    required String text,
    Color? customColor,
    bool isDanger = false,
  }) {
    Color bgColor;
    Color txtColor;

    if (customColor != null) {
      bgColor = customColor;
      txtColor = Colors.white;
    } else if (isDanger) {
      bgColor = Colors.red.shade100;
      txtColor = Colors.red.shade800;
    } else {
      bgColor = Colors.grey.shade100;
      txtColor = Colors.grey.shade800;
    }

    return BubbleStatusDisplay(
      text: text,
      backgroundColor: bgColor,
      textColor: txtColor,
      fontSize: 12,
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      borderRadius: BorderRadius.circular(12),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey.shade100,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor ?? Colors.grey.shade800,
          fontSize: fontSize ?? 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

/// 多个气泡状态显示组件的包装器
class MultipleBubbleDisplay extends StatelessWidget {
  final List<Widget> bubbles;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final double spacing;
  final bool wrap;

  const MultipleBubbleDisplay({
    Key? key,
    required this.bubbles,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.spacing = 8.0,
    this.wrap = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (wrap) {
      return Wrap(
        spacing: spacing,
        runSpacing: spacing,
        children: bubbles,
      );
    } else {
      return Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: _buildRowChildren(),
      );
    }
  }

  List<Widget> _buildRowChildren() {
    final children = <Widget>[];
    for (int i = 0; i < bubbles.length; i++) {
      children.add(bubbles[i]);
      if (i < bubbles.length - 1) {
        children.add(SizedBox(width: spacing));
      }
    }
    return children;
  }
}

/// 健康检测气泡列表组件
class HealthDetectionBubbles extends StatelessWidget {
  final List<String> detections;
  final bool showDangerMark;
  final String? dangerText;

  const HealthDetectionBubbles({
    Key? key,
    required this.detections,
    this.showDangerMark = false,
    this.dangerText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (detections.isEmpty) {
      return BubbleStatusDisplay.health(
        text: '正常',
        customColor: Colors.green.shade600,
      );
    }

    final bubbles = <Widget>[];

    for (final detection in detections) {
      final isDanger = showDangerMark && detection == (dangerText ?? '发热');

      if (isDanger) {
        // 发热检测结果，添加红色"危"字标记
        bubbles.add(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BubbleStatusDisplay.health(
                text: detection,
                isDanger: true,
              ),
              SizedBox(width: 4),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '危',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        bubbles.add(
          BubbleStatusDisplay.health(
            text: detection,
          ),
        );
      }
    }

    return MultipleBubbleDisplay(
      bubbles: bubbles,
      wrap: true,
      spacing: 6,
    );
  }
}

/// 带颜色的健康检测气泡列表组件
class ColoredHealthDetectionBubbles extends StatelessWidget {
  final List<dynamic> detections; // 支持 HealthDetection 枚举类型
  final bool showDangerMark;
  final String? dangerText;

  const ColoredHealthDetectionBubbles({
    Key? key,
    required this.detections,
    this.showDangerMark = false,
    this.dangerText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (detections.isEmpty) {
      return BubbleStatusDisplay.health(
        text: '正常',
        customColor: Colors.green.shade600,
      );
    }

    final bubbles = <Widget>[];

    for (final detection in detections) {
      // 获取检测名称和颜色
      String detectionName;
      Color detectionColor;

      if (detection is String) {
        detectionName = detection;
        detectionColor = Colors.grey.shade600; // 默认颜色
      } else {
        // 假设是 HealthDetection 枚举
        detectionName = detection.displayName;
        detectionColor =
            Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
      }

      final isDanger = showDangerMark && detectionName == (dangerText ?? '发热');

      if (isDanger) {
        // 发热检测结果，添加红色"危"字标记
        bubbles.add(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BubbleStatusDisplay.health(
                text: detectionName,
                customColor: detectionColor,
              ),
              SizedBox(width: 4),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '危',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        bubbles.add(
          BubbleStatusDisplay.health(
            text: detectionName,
            customColor: detectionColor,
          ),
        );
      }
    }

    return MultipleBubbleDisplay(
      bubbles: bubbles,
      wrap: true,
      spacing: 6,
    );
  }
}
