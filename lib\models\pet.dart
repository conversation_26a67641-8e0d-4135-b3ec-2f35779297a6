import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/user_context.dart';
import '../utils/app_logger.dart';

/// 宠物种类枚举
enum PetSpecies {
  dog('dog', '狗狗'),
  cat('cat', '猫咪');

  const PetSpecies(this.value, this.displayName);
  final String value;
  final String displayName;

  static PetSpecies fromString(String value) {
    return PetSpecies.values.firstWhere(
      (species) => species.value == value,
      orElse: () => PetSpecies.dog,
    );
  }
}

/// 宠物体型枚举
enum PetSize {
  small('small', '小型'),
  medium('medium', '中型'),
  large('large', '大型');

  const PetSize(this.value, this.displayName);
  final String value;
  final String displayName;

  static PetSize fromString(String value) {
    return PetSize.values.firstWhere(
      (size) => size.value == value,
      orElse: () => PetSize.small,
    );
  }
}

class Pet {
  final String nickname;
  final PetSpecies species;
  final String breed;
  final PetSize size;
  final DateTime birthday;
  final double shoulderHeight; // 肩高，单位：厘米
  final double weight; // 体重，单位：千克
  final int? id; // 可选的唯一标识符

  Pet({
    required this.nickname,
    required this.species,
    required this.breed,
    required this.birthday,
    required this.shoulderHeight,
    required this.weight,
    this.id,
  }) : size = _calculateSize(species, shoulderHeight, weight);

  /// 根据种类、肩高和体重自动计算体型
  static PetSize _calculateSize(
      PetSpecies species, double shoulderHeight, double weight) {
    // 猫咪统一定义为小型
    if (species == PetSpecies.cat) {
      return PetSize.small;
    }

    // 狗狗根据肩高和体重划分体型
    // 参考深圳市标准：
    // 小型犬：肩高25-40厘米，或者体重4-10千克
    // 中型犬：肩高40-60厘米，或者体重10-30千克
    // 大型犬：肩高60厘米以上，或者体重30千克以上
    if (shoulderHeight >= 60 || weight >= 30) {
      return PetSize.large;
    } else if (shoulderHeight >= 40 || weight >= 10) {
      return PetSize.medium;
    } else {
      return PetSize.small;
    }
  }

  /// 计算年龄（年）
  int get age {
    final now = DateTime.now();
    int age = now.year - birthday.year;
    if (now.month < birthday.month ||
        (now.month == birthday.month && now.day < birthday.day)) {
      age--;
    }
    return age;
  }

  /// 计算年龄描述（更详细）
  String get ageDescription {
    final now = DateTime.now();
    final difference = now.difference(birthday);
    final days = difference.inDays;

    if (days < 30) {
      return '${days}天';
    } else if (days < 365) {
      final months = (days / 30).floor();
      return '${months}个月';
    } else {
      final years = (days / 365).floor();
      final remainingMonths = ((days % 365) / 30).floor();
      if (remainingMonths > 0) {
        return '${years}岁${remainingMonths}个月';
      } else {
        return '${years}岁';
      }
    }
  }

  factory Pet.fromJson(Map<String, dynamic> json) {
    return Pet(
      id: json['id'],
      nickname: json['nickname'] ?? json['name'] ?? '', // 兼容旧字段名
      species: PetSpecies.fromString(json['species'] ?? 'dog'),
      breed: json['breed'] ?? '',
      birthday: DateTime.parse(json['birthday']),
      shoulderHeight:
          (json['shoulderHeight'] ?? json['height'] ?? 0).toDouble(), // 兼容旧字段名
      weight: (json['weight'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'nickname': nickname,
        'species': species.value,
        'breed': breed,
        'size': size.value,
        'birthday': birthday.toIso8601String(),
        'shoulderHeight': shoulderHeight,
        'weight': weight,
      };

  /// 保存宠物信息到本地存储
  static Future<void> savePet(Pet pet, {String key = 'petInfo'}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserContext.instance.getUserKey(key);
      String jsonString = jsonEncode(pet.toJson());
      await asyncPrefs.setString(storageKey, jsonString);
      AppLogger.info('宠物信息已保存: ${pet.nickname}');
    } catch (e, stackTrace) {
      AppLogger.error('保存宠物信息失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 从本地存储获取宠物信息
  static Future<Pet?> getPet({String key = 'petInfo'}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserContext.instance.getUserKey(key);
      String? jsonString = await asyncPrefs.getString(storageKey);
      if (jsonString != null) {
        return Pet.fromJson(jsonDecode(jsonString));
      }
      return null;
    } catch (e, stackTrace) {
      AppLogger.error('获取宠物信息失败: $e', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// 删除宠物信息
  static Future<void> deletePet({String key = 'petInfo'}) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final storageKey = UserContext.instance.getUserKey(key);
      await asyncPrefs.remove(storageKey);
      AppLogger.info('宠物信息已删除');
    } catch (e, stackTrace) {
      AppLogger.error('删除宠物信息失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 创建副本，用于编辑
  Pet copyWith({
    int? id,
    String? nickname,
    PetSpecies? species,
    String? breed,
    DateTime? birthday,
    double? shoulderHeight,
    double? weight,
  }) {
    return Pet(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      species: species ?? this.species,
      breed: breed ?? this.breed,
      birthday: birthday ?? this.birthday,
      shoulderHeight: shoulderHeight ?? this.shoulderHeight,
      weight: weight ?? this.weight,
    );
  }
}
