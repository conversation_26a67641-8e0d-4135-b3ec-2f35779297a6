import 'dart:async';
import 'dart:convert';

import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:pet_care/models/user.dart';
import 'package:provider/provider.dart';

import '../constants/constants.dart';
import '../utils/app_logger.dart';
import '../providers/auth_provider.dart';

// 自定义异常类，用于处理 API 请求错误
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic errorData;

  ApiException(this.message, {this.statusCode, this.errorData});

  @override
  String toString() {
    return 'ApiException: $message (Status code: $statusCode)';
  }
}

// ApiService 负责与后端 API 进行交互，包括数据的获取和提交。
class ApiService {
  // 检查网络连接是否可用
  Future<void> _checkNetwork() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      throw ApiException('手机网络连接不可用');
    }
  }

  /// 普通请求使用json格式，且决定是否使用token
  Future<Map<String, String>> _getHeaders({bool useToken = true}) async {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };

    if (!useToken) {
      return headers;
    }

    final authProvider =
        Provider.of<AuthProvider>(navigatorKey.currentContext!, listen: false);
    User? user = authProvider.user;
    if (user != null) {
      // 检查 token 是否过期
      if (JwtDecoder.isExpired(user.accessToken)) {
        // token 过期，尝试刷新
        await authProvider.refreshToken();
        // 获取新的token
        user = authProvider.user!;
      }

      headers['Authorization'] = 'Bearer ${user.accessToken}';
    }
    return headers;
  }

  // GET 请求
  Future<Map<String, dynamic>> getRequest(String endpoint,
      {Map<String, String>? headers, bool useToken = true}) async {
    await _checkNetwork();

    final String url = endpoint;
    final Map<String, String> requestHeaders =
        await _getHeaders(useToken: useToken);
    requestHeaders.addAll(headers ?? {});

    try {
      final response = await http
          .get(
            Uri.parse(url),
            headers: requestHeaders,
          )
          .timeout(
            Duration(seconds: AppConfig.requestTimeout),
          );
      return _handleResponse(response);
    } catch (e, stackTrace) {
      AppLogger.error('GET 请求错误：$e', error: e, stackTrace: stackTrace);
      throw ApiException('连接不到服务器', statusCode: 101, errorData: e);
    }
  }

  // POST 请求
  Future<Map<String, dynamic>> postRequest(
      String endpoint, Map<String, dynamic> body,
      {Map<String, String>? headers, bool useToken = true}) async {
    await _checkNetwork();
    AppLogger.info('POST 请求：$endpoint');
    AppLogger.info('POST 请求：$body');
    final String url = endpoint;
    final Map<String, String> requestHeaders =
        await _getHeaders(useToken: useToken);
    requestHeaders.addAll(headers ?? {});

    try {
      final response = await http
          .post(
            Uri.parse(url),
            headers: requestHeaders,
            body: jsonEncode(body),
          )
          .timeout(
            Duration(seconds: AppConfig.requestTimeout),
          );
      return _handleResponse(response);
    } catch (e, stackTrace) {
      AppLogger.error('POST 请求错误：$e', error: e, stackTrace: stackTrace);
      throw ApiException('连接不到服务器', statusCode: 101, errorData: e);
    }
  }

  // POST 请求 提交文件
  Future<Map<String, dynamic>> postFileRequest(
    String endpoint,
    String filePath,
    String fileField, {
    Map<String, String>? fields,
    Map<String, String>? headers,
    bool useToken = true,
  }) async {
    await _checkNetwork();

    final String url = endpoint;
    try {
      // 创建 MultipartRequest
      final request = http.MultipartRequest('POST', Uri.parse(url));

      // 添加文件
      final file = await http.MultipartFile.fromPath(fileField, filePath);
      request.files.add(file);

      // 添加其他字段
      if (fields != null) {
        request.fields.addAll(fields);
      }

      // 获取包含token的请求头
      final Map<String, String> requestHeaders =
          await _getHeaders(useToken: useToken);
      requestHeaders.addAll(headers ?? {});

      // 添加请求头
      request.headers.addAll(requestHeaders);

      // 发送请求并等待响应
      final streamedResponse = await request.send().timeout(
            Duration(seconds: AppConfig.requestTimeout),
          );

      // 将 StreamedResponse 转换为 Response
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse(response);
    } on TimeoutException catch (e, stackTrace) {
      AppLogger.error('文件上传请求超时：$e', error: e, stackTrace: stackTrace);
      throw ApiException('请求超时', statusCode: 102, errorData: e);
    } catch (e, stackTrace) {
      AppLogger.error('文件上传请求错误：$e', error: e, stackTrace: stackTrace);
      throw ApiException('连接不到服务器', statusCode: 101, errorData: e);
    }
  }

  // 处理认证错误
  void _handleAuthError() {
    final authProvider =
        Provider.of<AuthProvider>(navigatorKey.currentContext!, listen: false);
    authProvider.clearUser();
    // 使用pushReplacementNamed方法确保用户不能返回到当前页面
    // 当认证失败时，会清除用户信息并直接跳转到登录页面
    navigatorKey.currentState?.pushReplacementNamed(Routes.login);
  }

  // 状态码响应处理
  Map<String, dynamic> _handleResponse(http.Response response) {
    AppLogger.debug('返回状态码是：${response.statusCode}');
    AppLogger.debug('返回内容是：${response.body}');
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else if (response.statusCode == 401) {
      _handleAuthError();
      return {
        'code': response.statusCode,
        'message': '认证失败',
      };
    } else if (response.statusCode == 597) {
      return {
        'code': response.statusCode,
        'message': '设备短休眠',
      };
    } else if (response.statusCode == 598) {
      return {
        'code': response.statusCode,
        'message': '设备长休眠',
      };
    } else if (response.statusCode == 599) {
      return {
        'code': response.statusCode,
        'message': '设备离线',
      };
    } else {
      String errorMsg = '请求失败，状态码：${response.statusCode}';
      AppLogger.error(errorMsg,
          error: Exception(errorMsg), stackTrace: StackTrace.current);
      return {
        'code': response.statusCode,
        'message': errorMsg,
      };
    }
  }
}
