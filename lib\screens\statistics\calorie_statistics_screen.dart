import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 卡路里统计界面
class CalorieStatisticsScreen extends StatefulWidget {
  @override
  _CalorieStatisticsScreenState createState() =>
      _CalorieStatisticsScreenState();
}

class _CalorieStatisticsScreenState extends State<CalorieStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  DateTime _currentDate = DateTime.now();
  Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false)
            .device
            ?.deviceName ??
        '';
    ChartService chartService =
        Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchCalorieChartData(
              device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchCalorieChartData(
              device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchCalorieChartData(
              device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('卡路里统计'),
        backgroundColor: Colors.orange.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表区域
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : Container(
                  width: double.infinity,
                  height: 350,
                  margin: EdgeInsets.symmetric(vertical: 16),
                  child: Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '卡路里消耗趋势',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 16),
                          Expanded(
                            child: _buildChart(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
          // 统计信息
          // if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData['calories']!.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    // 根据时间段自动选择图表类型
    // 日模式和月模式显示曲线图，周模式显示柱状图
    switch (_selectedPeriod) {
      case TimePeriod.week:
        return _buildBarChart();
      case TimePeriod.day:
      case TimePeriod.month:
      default:
        return _buildLineChart();
    }
  }

  /// 构建曲线图
  Widget _buildLineChart() {
    // 构建FlSpot列表
    List<FlSpot> spots = [];
    double k = 0;
    for (final data in _healthData['calories']!) {
      spots.add(FlSpot(k, data.toDouble()));
      k++;
    }

    // 统一Y轴范围与网格/标题间隔，确保对齐
    final double interval = _getSideTitleInterval();
    final double maxValue =
        _healthData['calories']!.reduce((a, b) => a > b ? a : b).toDouble();
    double maxY = ((maxValue / interval).ceil() * interval);
    if (maxY == 0) maxY = interval; // 避免全0数据时看不到网格

    return LineChart(
      LineChartData(
        minX: 0,
        maxY: maxY,
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: interval,
          verticalInterval: null,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
              interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                  child: Text(
                    _getBottomTitle(value.toInt()),
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
              interval: _getSideTitleInterval(),
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  space: 6, // 与绘图区的间距
                  child: Text(
                    value.toInt().toString(),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    textAlign: TextAlign.left,
                  ),
                );
              },
            ),
          ), // 显示右侧卡路里标题
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final index = barSpot.x.toInt();
                String timeRange = '';

                if (_selectedPeriod == TimePeriod.day) {
                  // 日模式显示时间段
                  final hour = index ~/ 4;
                  final minute = (index % 4) * 15;
                  final nextMinute = minute + 15;
                  if (nextMinute == 60) {
                    timeRange =
                        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                  } else {
                    timeRange =
                        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                  }
                } else if (_selectedPeriod == TimePeriod.month) {
                  // 月模式显示具体日期
                  final day = index + 1;
                  final month = _currentDate.month; // 使用当前月份
                  timeRange = '${month}月${day}日';
                } else {
                  // 周模式显示具体日期
                  DateTime monday = _currentDate
                      .subtract(Duration(days: _currentDate.weekday - 1));
                  final weekdays = List.generate(
                      7,
                      (index) => DateTime(
                            monday.year,
                            monday.month,
                            monday.day + index, // Dart 会自动处理日期溢出
                          ));
                  timeRange =
                      '${weekdays[index].month}月${weekdays[index].day}日';
                }

                return LineTooltipItem(
                  '$timeRange\n${barSpot.y.toInt()}kcal',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }).toList();
            },
          ),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true, // 使用光滑曲线
            curveSmoothness: 0.0, // 设置曲线平滑度，值越大越平滑
            color: Colors.orange,
            barWidth: 1.5,
            dotData: FlDotData(show: false), // 不显示曲线上的小圆点
            belowBarData: BarAreaData(
              show: true,
              color: Colors.orange.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建柱状图
  Widget _buildBarChart() {
    // 构建BarChartGroupData列表
    List<BarChartGroupData> barGroups = [];

    int len = _healthData['calories']!.length;
    if (len < 7) {
      // 如果数据不足7天，补全到7天
      _healthData['calories']!.addAll(List.filled((7 - len), 0.0));
    }
    for (final y in _healthData['calories']!) {
      barGroups.add(BarChartGroupData(
        x: _healthData['calories']!.indexOf(y),
        barRods: [
          BarChartRodData(
            toY: y.toDouble(),
            color: Colors.blue,
            width: 16,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      ));
    }

    // 统一Y轴范围与网格/标题间隔，确保对齐
    final double interval = _getSideTitleInterval();
    final double maxValue =
        _healthData['calories']!.reduce((a, b) => a > b ? a : b).toDouble();
    double maxY = ((maxValue / interval).ceil() * interval);
    if (maxY == 0) maxY = interval; // 避免全0数据时看不到网格

    return BarChart(
      BarChartData(
        minY: 0,
        maxY: maxY,
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          drawHorizontalLine: true,
          horizontalInterval: interval,
          verticalInterval: null,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
              interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                  child: Text(
                    _getBottomTitle(value.toInt()),
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  space: 6, // 与绘图区的间距
                  child: Text(
                    value.toInt().toString(),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    textAlign: TextAlign.left,
                  ),
                );
              },
            ),
          ), // 显示右侧卡路里标题
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              String timeRange = '';

              if (_selectedPeriod == TimePeriod.week) {
                // 周模式显示具体日期
                DateTime monday = _currentDate
                    .subtract(Duration(days: _currentDate.weekday - 1));
                final weekdays = List.generate(
                    7,
                    (index) => DateTime(
                          monday.year,
                          monday.month,
                          monday.day + index, // Dart 会自动处理日期溢出
                        ));
                timeRange =
                    '${weekdays[groupIndex].month}月${weekdays[groupIndex].day}日';
              }

              return BarTooltipItem(
                '$timeRange\n${rod.toY.toInt()}kcal',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        barGroups: barGroups,
      ),
    );
  }

  /// 获取底部标题间隔
  double _getBottomTitleInterval() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return 12.0; // 日模式：3小时间隔(12个15分钟)
      case TimePeriod.week:
        return 1.0; // 周模式：每天显示
      case TimePeriod.month:
        return 5.0; // 月模式：每5天显示一个标签
    }
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    List<String> weekdaysShort = [
      'Mon',
      'Tue',
      'Wed',
      'Thu',
      'Fri',
      'Sat',
      'Sun'
    ];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = index ~/ 4;
        final minute = (index % 4) * 15;
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return weekdaysShort[index];
      case TimePeriod.month:
        return '${index + 1}日';
    }
  }

  ///获取侧边标题的间隔
  double _getSideTitleInterval() {
    // 取最大数据值，计算合适的间隔以显示5-6个标签
    if (_healthData['calories']!.isEmpty) return 10.0;

    double maxValue = _healthData['calories'].reduce((a, b) => a > b ? a : b);
    if (maxValue == 0) return 10.0;

    // 计算合适的间隔，确保有5-6个标签
    double rawInterval = maxValue / 5.0;

    // 将间隔调整为整数，便于对齐
    int interval;
    if (rawInterval <= 10) {
      interval = 10;
    } else {
      interval = ((rawInterval / 20).ceil() * 20).toInt();
    }

    return interval.toDouble();
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final totalCalories =
        _healthData['calories']!.fold(0, (sum, data) => sum + data);
    final avgCalories = _healthData['calories']!.isNotEmpty
        ? totalCalories / _healthData['calories']!.length
        : 0;
    final maxCalories = _healthData['calories']!.isNotEmpty
        ? _healthData['calories']!.reduce((a, b) => a > b ? a : b)
        : 0.0;

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('总消耗', '${totalCalories.toStringAsFixed(1)} kcal',
                  Colors.orange),
              _buildStatItem(
                  '平均', '${avgCalories.toStringAsFixed(1)} kcal', Colors.green),
              _buildStatItem(
                  '最高', '${maxCalories.toStringAsFixed(1)} kcal', Colors.red),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
