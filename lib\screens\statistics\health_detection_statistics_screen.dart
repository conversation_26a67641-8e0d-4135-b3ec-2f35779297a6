import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../constants/health_detection.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 健康监测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() =>
      _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState
    extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.multiLine;
  DateTime _currentDate = DateTime.now();
  Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  /// 多曲线图中各健康监测状态的可见性控制
  Map<HealthDetection, bool> _visibleDetections = {
    for (HealthDetection detection in HealthDetection.values)
      detection: HealthDetection.values.indexOf(detection) == 0 ? true : false,
  };

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false)
            .device
            ?.deviceName ??
        '';
    ChartService chartService =
        Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchHealthDetectionChartData(
              device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchHealthDetectionChartData(
              device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchHealthDetectionChartData(
              device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('异常健康监测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动切换图表类型
                final availableTypes =
                    ChartTypePresets.getHealthDetectionStatistics(period);
                if (availableTypes.isNotEmpty &&
                    !availableTypes.contains(_selectedChartType)) {
                  _selectedChartType = availableTypes.first;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: 16, left: 16, right: 8, bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getChartTitle(),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 16),
                          Expanded(
                            child: _buildChart(),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),

          // 多曲线图图例
          if (!_isLoading && _selectedPeriod == TimePeriod.month)
            _buildHealthDetectionColorLegend(),
          // 统计信息
          //if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 获取图表标题
  String _getChartTitle() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '异常状态发现';
      case TimePeriod.week:
        return '异常状态分布';
      case TimePeriod.month:
        return '异常状态走势';
      default:
        return '异常健康监测';
    }
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedPeriod) {
      case TimePeriod.day:
        return _buildScatterChart(); // 日模式使用散点图
      case TimePeriod.week:
        return Column(
          children: [
            SizedBox(
              height: 400,
              child: _buildGroupedBarChart(), // 周模式使用百分比堆叠图
            ),
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 8,
              runSpacing: 4,
              children: HealthDetection.values.map((detection) {
                final color =
                    Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
                return _buildLegendItem(detection.displayName, color);
              }).toList(),
            ),
          ],
        );
      case TimePeriod.month:
        _selectedChartType = ChartViewType.multiLine;
        return SizedBox(height: 200, width: 400, child: _buildMultiLineChart());
    }
  }

  /// 聚合健康监测数据按时间分组（用于散点图）
  Map<HealthDetection, List<FlSpot>> _aggregateHealthDetectionDataForScatter() {
    final Map<HealthDetection, List<FlSpot>> result = {};
    // 初始化所有监测项目的数据列表
    for (final detection in HealthDetection.values) {
      result[detection] = [];
      double x = 0;
      for (final data in _healthData[detection.displayName]) {
        // 只有当数据不为null且不为0时才添加散点
        if (data != null && data != 0) {
          result[detection]!.add(FlSpot(x, detection.severityLevel.toDouble()));
        }
        x++;
      }
    }
    return result;
  }

  /// 聚合健康监测数据按日期分组（用于多曲线图）
  Map<HealthDetection, List<FlSpot>> _aggregateHealthDetectionDataByDate() {
    final Map<HealthDetection, List<FlSpot>> result = {};
    // 初始化所有监测项目的数据列表
    for (final detection in HealthDetection.values) {
      double x = 0;
      for (final data in _healthData[detection.displayName]) {
        result[detection] ??= [];
        result[detection]!.add(FlSpot(x, data.toDouble()));
        x++;
      }
    }
    return result;
  }

  /// 构建散点图（健康监测趋势）
  Widget _buildScatterChart() {
    final aggregatedData = _aggregateHealthDetectionDataForScatter();

    // 使用所有监测项目数据
    final visibleData = aggregatedData;

    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return Column(
      children: [
        // 散点图区域
        Expanded(
          child: ScatterChart(
            ScatterChartData(
              gridData: FlGridData(
                show: true,
                horizontalInterval: 1.0, // 设置水平网格线间隔为1，对应严重等级1-10
                getDrawingHorizontalLine: (value) {
                  // 只在严重等级1-10的位置显示网格线，并使用对应健康监测项目的颜色
                  if (value >= 1 && value <= 10 && value % 1 == 0) {
                    final severityLevel = value.toInt();
                    final healthDetection = HealthDetection.values.firstWhere(
                      (detection) => detection.severityLevel == severityLevel,
                      orElse: () => HealthDetection.fever,
                    );
                    final color = Color(int.parse(
                        '0xFF${healthDetection.colorHex.substring(1)}'));

                    return FlLine(
                      color: color, // 使用健康监测项目颜色，最高透明度
                      strokeWidth: 1,
                      dashArray: [5, 5], // 设置虚线样式
                    );
                  }
                  return FlLine(color: Colors.transparent, strokeWidth: 0);
                },
                getDrawingVerticalLine: (value) {
                  return FlLine(
                    color: Colors.grey.shade300,
                    strokeWidth: 1,
                    dashArray: [5, 5],
                  );
                },
              ),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                    interval: 12.0, // 每12个点显示一次标签（6小时间隔）
                    getTitlesWidget: (value, meta) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                        child: Text(
                          _getBottomTitleForScatter(value.toInt()),
                          style: TextStyle(
                              fontSize: 10, color: Colors.grey.shade600),
                        ),
                      );
                    },
                  ),
                ),
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 60, // 减少预留空间，因为使用斜向文字
                    interval: 1.0, // 每个严重等级显示一个标签
                    getTitlesWidget: (value, meta) {
                      final severityLevel = value.toInt();
                      if (severityLevel >= 1 && severityLevel <= 10) {
                        final healthDetection =
                            HealthDetection.values.firstWhere(
                          (detection) =>
                              detection.severityLevel == severityLevel,
                          orElse: () => HealthDetection.fever,
                        );
                        return Transform.rotate(
                          angle: -0.5, // 斜向显示，约-30度
                          child: Container(
                            width: 50, // 减少宽度
                            padding: const EdgeInsets.only(left: 2.0),
                            child: Text(
                              healthDetection.displayName,
                              style: TextStyle(
                                fontSize: 8, // 稍微减小字体
                                color: Colors.grey.shade700,
                              ),
                              textAlign: TextAlign.left,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        );
                      }
                      return SizedBox.shrink();
                    },
                  ),
                ),
                topTitles:
                    AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: false), // 去掉坐标轴外框
              scatterTouchData: ScatterTouchData(
                enabled: true,
                handleBuiltInTouches: true, // 确保内置触摸处理正常
                touchSpotThreshold: 10, // 增加触摸容差，使触摸更容易
                touchTooltipData: ScatterTouchTooltipData(
                  fitInsideHorizontally: true,
                  fitInsideVertically: true,
                  tooltipHorizontalOffset: -12, // 向图表内部偏移，避免被右侧标签遮挡
                  getTooltipItems: (ScatterSpot touchedBarSpot) {
                    final index = touchedBarSpot.x.toInt();
                    String timeRange = '';

                    // 日模式显示时间段（30分钟间隔）
                    final hour = index ~/ 2;
                    final minute = (index % 2) * 30;
                    final nextMinute = minute + 30;
                    if (nextMinute == 60) {
                      timeRange =
                          '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                    } else {
                      timeRange =
                          '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                    }

                    // 根据Y值确定健康监测状态
                    final severityLevel = touchedBarSpot.y.toInt();
                    String healthText;

                    if (severityLevel == 0) {
                      healthText = '无数据或无异常';
                    } else {
                      final healthDetection = HealthDetection.values.firstWhere(
                        (detection) => detection.severityLevel == severityLevel,
                        orElse: () => HealthDetection.fever,
                      );
                      healthText = healthDetection.displayName;
                    }

                    return ScatterTooltipItem(
                      '$timeRange\n$healthText',
                      textStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
              scatterSpots: _buildScatterSpots(visibleData),
              minX: 0,
              maxX: 47, // 24小时 * 2（30分钟间隔）- 1
              minY: 0,
              maxY: _getMaxYValueForScatter(visibleData),
            ),
          ),
        ),
        // 底部图例
        _buildBottomLegend(),
      ],
    );
  }

  /// 构建底部图例
  Widget _buildBottomLegend() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Wrap(
        spacing: 12.0,
        runSpacing: 8.0,
        alignment: WrapAlignment.center,
        children: HealthDetection.values.map((detection) {
          final color =
              Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 4),
              Text(
                detection.displayName,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 构建周模式百分比堆叠柱状图（10种健康监测状态分布）
  Widget _buildGroupedBarChart() {
    // 构建BarChartGroupData列表
    final barGroups = <BarChartGroupData>[];

    for (int i = 0; i < 7; i++) {
      // 计算当天所有健康监测状态的总数
      int totalCount = 0;
      final dayData = <HealthDetection, int>{};

      for (final detection in HealthDetection.values) {
        // 数据补全逻辑：检查数组长度，避免越界异常
        final detectionData = _healthData[detection.displayName] as List?;
        final count = (detectionData != null && i < detectionData.length)
            ? detectionData[i] as int
            : 0; // 如果数据不足7天，用0补全缺失的天数
        dayData[detection] = count;
        totalCount += count;
      }

      // 如果当天没有数据，显示空柱子
      if (totalCount == 0) {
        barGroups.add(
          BarChartGroupData(
            x: i,
            barRods: [
              BarChartRodData(
                fromY: 0,
                toY: 100,
                color: Colors.grey.shade300,
                width: 30,
                borderRadius: BorderRadius.circular(0),
              ),
            ],
          ),
        );
        continue;
      }

      // 构建单个堆叠柱状图的数据段
      final stackedSegments = <BarChartRodStackItem>[];
      double currentY = 0;

      for (final detection in HealthDetection.values) {
        final count = dayData[detection]!;
        final percentage = (count / totalCount) * 100;

        if (percentage > 0) {
          final color =
              Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
          stackedSegments.add(
            BarChartRodStackItem(
              currentY,
              currentY + percentage,
              color,
            ),
          );
          currentY += percentage;
        }
      }

      // 创建单个堆叠柱状图
      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              fromY: 0,
              toY: 100,
              color: Colors.transparent, // 背景透明
              width: 30,
              borderRadius: BorderRadius.circular(0),
              rodStackItems: stackedSegments,
            ),
          ],
        ),
      );
    }

    return BarChart(
      BarChartData(
        gridData: FlGridData(
          show: true,
          horizontalInterval: 25, // 设置水平网格线间隔为25%
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.transparent, // 设置为透明色，不显示竖线
              strokeWidth: 0,
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 35, // 增加空间以显示百分比符号
              interval: 25, // 设置Y轴间隔为25%
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toInt()}%',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  textAlign: TextAlign.right,
                );
              },
            ),
          ), // 显示右侧百分比标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
              getTitlesWidget: (value, meta) {
                return Text(
                  _getGroupTitle(value.toInt()),
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        maxY: 100, // 设置Y轴最大值为100%
        minY: 0, // 设置Y轴最小值为0%
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final date =
                  _currentDate.subtract(Duration(days: 6 - groupIndex));

              final groupTitle = '${date.month}/${date.day}';

              // 计算当天所有健康监测状态的总数和当前状态的数据
              int totalCount = 0;
              final dayData = <HealthDetection, int>{};

              for (final detection in HealthDetection.values) {
                // 数据补全逻辑：检查数组长度，避免越界异常
                final detectionData =
                    _healthData[detection.displayName] as List?;
                final count =
                    (detectionData != null && groupIndex < detectionData.length)
                        ? detectionData[groupIndex] as int
                        : 0; // 如果数据不足7天，用0补全缺失的天数
                dayData[detection] = count;
                totalCount += count;
              }

              if (totalCount == 0) {
                return BarTooltipItem(
                  '$groupTitle\n无健康监测数据',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }

              // 显示当天所有健康监测状态的详细信息（两列布局）
              final tooltipLines = <String>[groupTitle];

              // 收集有数据的健康监测项目
              final activeDetections = <MapEntry<HealthDetection, int>>[];
              for (final detection in HealthDetection.values) {
                final count = dayData[detection]!;
                if (count > 0) {
                  activeDetections.add(MapEntry(detection, count));
                }
              }

              // 限制显示的项目数量，避免tooltip过长
              final maxItems = 8; // 最多显示8个项目（4行，每行2个）
              final displayItems = activeDetections.take(maxItems).toList();

              // 如果有数据，按两列格式排列
              if (displayItems.isNotEmpty) {
                for (int i = 0; i < displayItems.length; i += 2) {
                  final leftEntry = displayItems[i];
                  final leftDetection = leftEntry.key;
                  final leftCount = leftEntry.value;
                  final leftPercentage = (leftCount / totalCount) * 100;

                  // 格式化左列：项目名(简化) 百分比
                  String leftName = _getShortName(leftDetection.displayName);
                  String leftText =
                      '$leftName ${leftPercentage.toStringAsFixed(1)}%';

                  // 检查是否有右列数据
                  if (i + 1 < displayItems.length) {
                    final rightEntry = displayItems[i + 1];
                    final rightDetection = rightEntry.key;
                    final rightCount = rightEntry.value;
                    final rightPercentage = (rightCount / totalCount) * 100;

                    String rightName =
                        _getShortName(rightDetection.displayName);
                    String rightText =
                        '$rightName ${rightPercentage.toStringAsFixed(1)}%';

                    // 使用制表符分隔两列，确保对齐
                    tooltipLines.add('$leftText\t\t$rightText');
                  } else {
                    // 只有左列数据
                    tooltipLines.add(leftText);
                  }
                }

                // 如果有更多项目未显示，添加提示
                if (activeDetections.length > maxItems) {
                  final remaining = activeDetections.length - maxItems;
                  tooltipLines.add('...还有${remaining}项');
                }
              }

              return BarTooltipItem(
                tooltipLines.join('\n'),
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 11,
                ),
              );
            },
          ),
        ),
        barGroups: barGroups,
      ),
    );
  }

  /// 获取散点图底部标题（30分钟间隔）
  String _getBottomTitleForScatter(int index) {
    final hour = index ~/ 2;
    final minute = (index % 2) * 30;
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// 获取散点图Y轴最大值
  double _getMaxYValueForScatter(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 构建散点图的散点数据
  List<ScatterSpot> _buildScatterSpots(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    final List<ScatterSpot> scatterSpots = [];

    for (final entry in visibleData.entries) {
      final detection = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

      for (final spot in spots) {
        scatterSpots.add(
          ScatterSpot(
            spot.x,
            spot.y,
            dotPainter: FlDotCirclePainter(
              radius: 3.0, // 增加散点大小以改善触摸体验
              color: color,
            ),
          ),
        );
      }
    }

    return scatterSpots;
  }

  /// 构建多曲线图（月模式：10条健康监测曲线）
  Widget _buildMultiLineChart() {
    final aggregatedData = _aggregateHealthDetectionDataByDate();

    // 过滤出可见的监测项目数据
    final visibleData = <HealthDetection, List<FlSpot>>{};
    for (final entry in aggregatedData.entries) {
      if (_visibleDetections[entry.key] == true && entry.value.isNotEmpty) {
        visibleData[entry.key] = entry.value;
      }
    }
    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          horizontalInterval: _getYAxisInterval(visibleData),
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 12, // 减少预留空间，旋转后减小左侧空白
              interval: 6.0, // 每6天显示一个标签
              getTitlesWidget: (value, meta) {
                final day = value.toInt() + 1;
                return Text(
                  '${day}日',
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 进一步减少右侧预留空间，让绘图区域更靠近右边缘
              interval: _getYAxisInterval(visibleData), // 设置Y轴间隔
              getTitlesWidget: (value, meta) {
                final interval = _getYAxisInterval(visibleData).toInt();
                final intValue = value.toInt();

                // 只显示符合间隔的非负整数值
                if (intValue >= 0 && interval > 0 && intValue % interval == 0) {
                  return Text(
                    intValue.toString(),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    textAlign: TextAlign.right,
                  );
                }
                return SizedBox.shrink(); // 不显示非间隔值
              },
            ),
          ), // 显示右侧数值标题
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        lineTouchData: LineTouchData(
          enabled: true,
          handleBuiltInTouches: true, // 确保内置触摸处理正常
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return _buildMultiLineTooltipItems(touchedBarSpots);
            },
          ),
          touchCallback:
              (FlTouchEvent event, LineTouchResponse? touchResponse) {
            // 不执行任何状态更新，只处理tooltip显示
            // 这样可以避免触摸时意外的状态改变
          },
        ),
        lineBarsData: _buildMultiLineChartBars(visibleData),
        maxY: _getMaxYValue(visibleData), // 设置Y轴最大值
        minY: 0, // 设置Y轴最小值
      ),
    );
  }

  /// 获取Y轴最大值（用于多曲线图）
  double _getMaxYValue(Map<HealthDetection, List<FlSpot>> visibleData) {
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 获取Y轴间隔（用于多曲线图）
  double _getYAxisInterval(Map<HealthDetection, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 1.0;

    final maxValue = _getMaxYValue(visibleData);

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建多曲线图的曲线数据
  List<LineChartBarData> _buildMultiLineChartBars(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    final List<LineChartBarData> lineBars = [];

    for (final entry in visibleData.entries) {
      final detection = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

      lineBars.add(
        LineChartBarData(
          spots: spots,
          isCurved: true, // 使用光滑曲线
          curveSmoothness: 0.0, // 设置曲线平滑度，值越大越平滑
          color: color,
          barWidth: 1.5, // 线条宽度
          dotData: FlDotData(show: false), // 不显示曲线上的小圆点
          belowBarData: BarAreaData(show: false), // 多曲线图不显示填充区域
        ),
      );
    }

    return lineBars;
  }

  /// 构建多曲线图的tooltip项
  List<LineTooltipItem> _buildMultiLineTooltipItems(
      List<LineBarSpot> touchedBarSpots) {
    final List<LineTooltipItem> tooltipItems = [];

    if (touchedBarSpots.isNotEmpty) {
      final index = touchedBarSpots.first.x.toInt();
      final day = index + 1;
      final month = DateTime.now().month; // 使用当前月份
      final timeRange = '${month}月${day}日';

      // 为每个触摸点创建对应的tooltip项
      final visibleDetections = HealthDetection.values
          .where((detection) => _visibleDetections[detection] == true)
          .toList();

      for (int i = 0; i < touchedBarSpots.length; i++) {
        final barSpot = touchedBarSpots[i];

        if (i < visibleDetections.length) {
          final detection = visibleDetections[i];
          final color =
              Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

          // 第一个tooltip项包含时间范围，其他项只显示状态信息
          final displayText = i == 0
              ? '$timeRange\n${detection.displayName}: ${barSpot.y.toInt()}次'
              : '${detection.displayName}: ${barSpot.y.toInt()}次';

          tooltipItems.add(
            LineTooltipItem(
              displayText,
              TextStyle(
                color: i == 0 ? Colors.white : color,
                fontWeight: i == 0 ? FontWeight.bold : FontWeight.w600,
                fontSize: i == 0 ? 12 : 11,
              ),
            ),
          );
        } else {
          // 如果没有对应的状态，添加空的tooltip项以保持数量一致
          tooltipItems.add(
            LineTooltipItem(
              '',
              const TextStyle(color: Colors.transparent),
            ),
          );
        }
      }
    }

    return tooltipItems;
  }

  /// 获取组标题（周模式）
  String _getGroupTitle(int index) {
    switch (index) {
      case 0:
        return '周一';
      case 1:
        return '周二';
      case 2:
        return '周三';
      case 3:
        return '周四';
      case 4:
        return '周五';
      case 5:
        return '周六';
      case 6:
        return '周日';
      default:
        return '${(index + 1)}日';
    }
  }

  /// 获取健康监测项目的简化名称（用于tooltip显示）
  String _getShortName(String fullName) {
    switch (fullName) {
      case '发热/热射病':
        return '发热';
      case '呼吸异常':
        return '呼吸异常';
      case '运动不平稳':
        return '运动不稳';
      case '步态不对称':
        return '步态不对称';
      case '步态规律性下降':
        return '步态规律↓';
      case '夜醒':
        return '夜醒';
      case '睡眠不足':
        return '睡眠不足';
      case '运动复杂度异常':
        return '运动复杂度';
      case '活动模式改变':
        return '活动模式';
      case 'kcal下降':
        return 'kcal↓';
      default:
        // 如果名称过长，截断并添加省略号
        return fullName.length > 6 ? '${fullName.substring(0, 5)}..' : fullName;
    }
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建健康监测颜色图例（月模式）
  Widget _buildHealthDetectionColorLegend() {
    return Card(
      elevation: 4, // 添加阴影效果
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // 设置圆角
      ),
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 设置外边距
      child: Container(
        height: 200,
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '健康监测项目',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 12),
            Expanded(
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: 12,
                runSpacing: 8,
                children: HealthDetection.values.map((detection) {
                  final color = Color(
                      int.parse('0xFF${detection.colorHex.substring(1)}'));
                  final isVisible = _visibleDetections[detection] ?? true;

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _visibleDetections[detection] = !isVisible;
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isVisible
                            ? color.withOpacity(0.1)
                            : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isVisible ? color : Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: isVisible ? color : Colors.grey.shade400,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 6),
                          Text(
                            detection.displayName,
                            style: TextStyle(
                              fontSize: 10,
                              color: isVisible
                                  ? Colors.grey.shade700
                                  : Colors.grey.shade500,
                              fontWeight: isVisible
                                  ? FontWeight.w500
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
