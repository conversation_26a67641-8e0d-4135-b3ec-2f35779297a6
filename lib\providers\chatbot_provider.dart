import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import '../services/chatbot_service.dart';
import '../utils/app_logger.dart';
import '../utils/app_storage.dart';

/// AI聊天状态管理
class ChatbotProvider with ChangeNotifier {
  final ChatbotService _chatbotService;

  ChatbotProvider(this._chatbotService);

  // 存储键名常量
  static const String _messagesKey = 'chatbot_messages';
  static const String _sessionIdKey = 'chatbot_session_id';

  // 是否已经初始化过（避免重复初始化）
  bool _isInitialized = false;

  /// 消息列表
  List<ChatMessage> _messages = [];
  List<ChatMessage> get messages => List.unmodifiable(_messages);

  /// 是否正在发送消息
  bool _isSending = false;
  bool get isSending => _isSending;

  /// AI是否正在输入
  bool _isAITyping = false;
  bool get isAITyping => _isAITyping;

  /// 当前流式输出的消息ID
  String? _currentStreamingMessageId;

  /// 流式输出的内容缓冲区
  final Map<String, StringBuffer> _streamBuffers = {};

  /// 流式输出的错误信息缓冲区
  final Map<String, String> _streamErrors = {};

  /// 流订阅管理
  StreamSubscription? _currentStreamSubscription;

  /// 获取当前会话ID
  String get currentSessionId => _chatbotService.currentSessionId;

  /// 初始化 - 智能加载历史对话
  Future<void> init() async {
    try {
      // 如果已经初始化过，不重复加载
      if (_isInitialized) {
        AppLogger.info('ChatbotProvider已初始化，跳过重复初始化');
        return;
      }

      // 尝试加载历史对话
      await _loadChatHistory();

      _isInitialized = true;
      AppLogger.info('ChatbotProvider初始化完成，消息数量: ${_messages.length}');
    } catch (e, stackTrace) {
      AppLogger.error('ChatbotProvider初始化失败：$e',
          error: e, stackTrace: stackTrace);
    }
  }

  /// 发送消息（使用流式处理）
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || _isSending) return;

    try {
      _isSending = true;
      notifyListeners();

      // 添加用户消息
      final userMessage =
          ChatMessage.user(content: content.trim(), status: MessageStatus.sent);
      _messages.add(userMessage);
      AppLogger.info('添加用户消息: ${userMessage.id}, 内容: ${userMessage.content}');
      notifyListeners();

      // 保存用户消息到历史记录
      await _saveChatHistory();

      // 设置AI正在输入状态
      _isAITyping = true;
      AppLogger.info('设置AI正在输入状态: $_isAITyping');
      notifyListeners();

      // 创建AI消息占位符
      final aiMessage = ChatMessage.ai(
        content: '',
        status: MessageStatus.sent, // 直接设置为sent状态，不使用typing
      );
      _messages.add(aiMessage);
      _currentStreamingMessageId = aiMessage.id;
      AppLogger.info('创建AI消息占位符: ${aiMessage.id}');

      // 初始化流缓冲区
      _streamBuffers[aiMessage.id] = StringBuffer();
      _streamErrors.remove(aiMessage.id);

      notifyListeners();

      // 获取SSE流并处理（使用当前会话ID）
      final stream = _chatbotService.sendMessageStream(
        content,
        sessionId: _chatbotService.currentSessionId,
      );

      _currentStreamSubscription = stream.listen(
        (data) {
          _handleStreamData(data);
        },
        onError: (error) {
          _handleStreamError(error);
        },
        onDone: () {
          _handleStreamComplete();
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('发送消息失败：$e', error: e, stackTrace: stackTrace);
      await _handleSendMessageError(e);
    } finally {
      _isSending = false;
      notifyListeners();
    }
  }

  /// 处理流数据
  void _handleStreamData(SSEResponseData data) {
    if (_currentStreamingMessageId == null) return;

    final messageId = _currentStreamingMessageId!;
    final buffer = _streamBuffers[messageId];
    if (buffer == null) return;

    // 处理错误信息
    if (data.hasError) {
      _streamErrors[messageId] = data.error!;
      notifyListeners();
      return;
    }

    // 处理内容数据
    if (data.hasContent) {
      buffer.write(data.content);

      // 更新消息内容 - 增加安全验证
      final messageIndex = _messages.indexWhere((msg) => msg.id == messageId);
      if (messageIndex != -1) {
        final targetMessage = _messages[messageIndex];
        // 确保只更新AI消息，避免错误更新用户消息
        if (!targetMessage.isUser && targetMessage.id == messageId) {
          _messages[messageIndex] = targetMessage.copyWith(
            content: buffer.toString(),
            status: MessageStatus.sent, // 直接设置为sent状态
          );
          notifyListeners();
          AppLogger.debug(
              '更新AI消息内容: ${buffer.toString().substring(0, buffer.toString().length > 50 ? 50 : buffer.toString().length)}...');
        } else {
          AppLogger.error(
              '尝试更新错误的消息类型: messageId=$messageId, isUser=${targetMessage.isUser}',
              error: 'Message type mismatch');
        }
      } else {
        AppLogger.error('未找到对应的消息: messageId=$messageId',
            error: 'Message not found');
      }
    }

    // 移除对isStreamComplete的检查，完全依赖SSE连接的onDone事件
    // 这样可以确保流结束检测更加可靠
  }

  /// 处理流错误
  void _handleStreamError(dynamic error) {
    AppLogger.error('SSE流错误: $error', error: error);

    if (_currentStreamingMessageId != null) {
      _streamErrors[_currentStreamingMessageId!] = '连接错误: $error';

      // 更新消息状态为失败 - 增加安全验证
      final messageIndex =
          _messages.indexWhere((msg) => msg.id == _currentStreamingMessageId);
      if (messageIndex != -1) {
        final targetMessage = _messages[messageIndex];
        // 确保只更新AI消息
        if (!targetMessage.isUser &&
            targetMessage.id == _currentStreamingMessageId) {
          _messages[messageIndex] = targetMessage.copyWith(
            status: MessageStatus.failed,
          );
        }
      }
    }

    // 重置AI输入状态
    _isAITyping = false;
    AppLogger.info('流错误处理：重置AI输入状态: $_isAITyping');
    _handleStreamComplete();
  }

  /// 处理流完成
  Future<void> _handleStreamComplete() async {
    AppLogger.info(
        '开始处理流完成，当前状态: _isAITyping=$_isAITyping, _currentStreamingMessageId=$_currentStreamingMessageId');

    // 将当前流式消息状态更新为已发送 - 增加安全验证
    if (_currentStreamingMessageId != null) {
      final messageIndex =
          _messages.indexWhere((msg) => msg.id == _currentStreamingMessageId);
      if (messageIndex != -1) {
        final targetMessage = _messages[messageIndex];
        // 确保只更新AI消息
        if (!targetMessage.isUser &&
            targetMessage.id == _currentStreamingMessageId) {
          _messages[messageIndex] = targetMessage.copyWith(
            status: MessageStatus.sent,
          );
          AppLogger.info('更新AI消息状态为已发送: ${targetMessage.id}');
        }
      }
    }

    // 确保重置所有相关状态
    _isAITyping = false;
    _currentStreamingMessageId = null;
    _currentStreamSubscription?.cancel();
    _currentStreamSubscription = null;

    AppLogger.info('流完成处理完毕：AI输入状态已重置: $_isAITyping');

    // 确保UI更新
    notifyListeners();

    // 保存完整对话到历史记录
    await _saveChatHistory();
  }

  /// 处理发送消息错误
  Future<void> _handleSendMessageError(dynamic error) async {
    AppLogger.error('发送消息错误处理: $error', error: error);

    // 更新用户消息状态为失败
    if (_messages.isNotEmpty && _messages.last.isUser) {
      final lastIndex = _messages.length - 1;
      _messages[lastIndex] = _messages[lastIndex].copyWith(
        status: MessageStatus.failed,
      );
    }

    // 移除可能的AI消息占位符
    if (_currentStreamingMessageId != null) {
      _messages.removeWhere((msg) => msg.id == _currentStreamingMessageId);
      _streamBuffers.remove(_currentStreamingMessageId);
      _streamErrors.remove(_currentStreamingMessageId);
      _currentStreamingMessageId = null;
    }

    // 确保重置所有状态
    _isAITyping = false;
    _currentStreamSubscription?.cancel();
    _currentStreamSubscription = null;
    notifyListeners();
  }

  /// 重新发送失败的消息
  Future<void> resendMessage(ChatMessage message) async {
    if (!message.isUser || message.status != MessageStatus.failed) return;

    // 移除失败的消息
    _messages.removeWhere((msg) => msg.id == message.id);
    notifyListeners();

    // 重新发送
    await sendMessage(message.content);
  }

  /// 获取消息的错误信息
  String? getMessageError(String messageId) {
    return _streamErrors[messageId];
  }

  /// 检查消息是否有错误
  bool hasMessageError(String messageId) {
    return _streamErrors.containsKey(messageId) &&
        _streamErrors[messageId]!.isNotEmpty;
  }

  /// 加载历史对话
  Future<void> _loadChatHistory() async {
    try {
      // 加载历史消息
      final messagesJson = await AppStorage.getUserString(_messagesKey);
      if (messagesJson != null && messagesJson.isNotEmpty) {
        final List<dynamic> messagesList = jsonDecode(messagesJson);
        _messages =
            messagesList.map((json) => ChatMessage.fromJson(json)).toList();
        AppLogger.info('已加载历史消息: ${_messages.length}条');
      }

      // 加载历史会话ID
      final savedSessionId = await AppStorage.getUserString(_sessionIdKey);
      if (savedSessionId != null && savedSessionId.isNotEmpty) {
        // 恢复会话ID到ChatbotService
        _chatbotService.restoreSession(savedSessionId);
        AppLogger.info('已恢复会话ID: $savedSessionId');
      }
    } catch (e, stackTrace) {
      AppLogger.error('加载历史对话失败：$e', error: e, stackTrace: stackTrace);
      // 加载失败时清空消息，避免显示错误数据
      _messages.clear();
    }
  }

  /// 保存对话历史
  Future<void> _saveChatHistory() async {
    try {
      // 保存消息列表
      final messagesJson =
          jsonEncode(_messages.map((msg) => msg.toJson()).toList());
      await AppStorage.setUserString(_messagesKey, messagesJson);

      // 保存当前会话ID
      await AppStorage.setUserString(
          _sessionIdKey, _chatbotService.currentSessionId);

      AppLogger.debug('对话历史已保存: ${_messages.length}条消息');
    } catch (e, stackTrace) {
      AppLogger.error('保存对话历史失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  /// 新建对话（清空当前对话并开始新的会话）
  Future<void> createNewConversation() async {
    try {
      // 取消当前的流订阅
      _currentStreamSubscription?.cancel();
      _currentStreamSubscription = null;

      // 清理流状态
      _currentStreamingMessageId = null;
      _streamBuffers.clear();
      _streamErrors.clear();
      _isAITyping = false;

      // 清空当前对话
      _messages.clear();

      // 创建新会话
      _chatbotService.createNewSession();

      // 清空存储的历史记录
      await AppStorage.removeUserData(_messagesKey);
      await AppStorage.removeUserData(_sessionIdKey);

      notifyListeners();
      AppLogger.info('已创建新对话，会话ID: ${_chatbotService.currentSessionId}');
    } catch (e, stackTrace) {
      AppLogger.error('创建新对话失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  /// 删除指定消息
  Future<void> deleteMessage(String messageId) async {
    try {
      _messages.removeWhere((msg) => msg.id == messageId);
      _streamBuffers.remove(messageId);
      _streamErrors.remove(messageId);

      // 保存更新后的消息列表
      await _saveChatHistory();
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('删除消息失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  /// 清空所有消息
  Future<void> clearAllMessages() async {
    await createNewConversation();
  }

  /// 获取消息数量
  int get messageCount => _messages.length;

  /// 是否有消息
  bool get hasMessages => _messages.isNotEmpty;

  /// 获取最后一条消息
  ChatMessage? get lastMessage => _messages.isNotEmpty ? _messages.last : null;

  @override
  void dispose() {
    // 在dispose时保存当前状态（页面切换时保持数据）
    _saveChatHistory().catchError((e) {
      AppLogger.error('dispose时保存对话历史失败：$e', error: e);
    });

    _currentStreamSubscription?.cancel();
    _streamBuffers.clear();
    _streamErrors.clear();

    // 释放chatbot服务资源
    _chatbotService.dispose();

    super.dispose();
  }
}
