import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';

part 'chat_message.g.dart';

/// 消息状态枚举
enum MessageStatus {
  @JsonValue('sending')
  sending, // 发送中
  @JsonValue('sent')
  sent, // 已发送
  @JsonValue('failed')
  failed, // 发送失败
  @JsonValue('typing')
  typing, // AI正在输入（打字机效果）
}

/// 聊天消息模型
@JsonSerializable()
class ChatMessage {
  /// 消息唯一标识
  final String id;

  /// 消息内容
  final String content;

  /// 是否为用户发送的消息（true: 用户消息, false: AI消息）
  final bool isUser;

  /// 消息发送时间戳
  final DateTime timestamp;

  /// 消息状态
  final MessageStatus status;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.status = MessageStatus.sent,
  });

  /// 从JSON创建ChatMessage实例
  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);

  /// UUID生成器
  static const Uuid _uuid = Uuid();

  /// 创建用户消息
  factory ChatMessage.user({
    required String content,
    MessageStatus status = MessageStatus.sending,
  }) {
    final now = DateTime.now();
    return ChatMessage(
      id: 'user_${_uuid.v4()}',
      content: content,
      isUser: true,
      timestamp: now,
      status: status,
    );
  }

  /// 创建AI消息
  factory ChatMessage.ai({
    required String content,
    MessageStatus status = MessageStatus.typing,
  }) {
    final now = DateTime.now();
    return ChatMessage(
      id: 'ai_${_uuid.v4()}',
      content: content,
      isUser: false,
      timestamp: now,
      status: status,
    );
  }

  /// 复制并修改消息状态
  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    MessageStatus? status,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatMessage(id: $id, content: $content, isUser: $isUser, timestamp: $timestamp, status: $status)';
  }
}
