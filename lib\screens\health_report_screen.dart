import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import '../widgets/time_period_selector.dart';
import '../providers/device_provider.dart';
import '../services/health_report_service.dart';

/// 健康分析报告界面
class HealthReportScreen extends StatefulWidget {
  @override
  _HealthReportScreenState createState() => _HealthReportScreenState();
}

class _HealthReportScreenState extends State<HealthReportScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  DateTime _currentDate = DateTime.now();
  String _reportContent = '';
  bool _isLoading = false;
  bool _isStreaming = false;
  HealthReportService? _reportService;

  @override
  void initState() {
    super.initState();
    _reportService = HealthReportService();
    _loadReport();
  }

  @override
  void dispose() {
    _reportService?.dispose();
    super.dispose();
  }

  /// 加载健康报告
  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
      _reportContent = '';
      _isStreaming = false;
    });

    try {
      // 获取设备名称
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);
      final deviceName = deviceProvider.device?.deviceName ?? '';

      if (deviceName.isEmpty) {
        setState(() {
          _reportContent = '## 错误\n\n请先连接设备后再查看健康报告。';
          _isLoading = false;
        });
        return;
      }

      // 开始流式接收报告数据
      setState(() {
        _isLoading = false;
        _isStreaming = true;
      });

      await _reportService!.getHealthReport(
        deviceName: deviceName,
        period: _selectedPeriod,
        date: _currentDate,
        onData: (chunk) {
          setState(() {
            _reportContent += chunk;
          });
        },
        onError: (error) {
          setState(() {
            if (_reportContent.isEmpty) {
              _reportContent = '## 错误\n\n生成健康报告时发生错误：$error';
            } else {
              _reportContent += '\n\n## 错误\n\n生成过程中发生错误：$error';
            }
            _isStreaming = false;
          });
        },
        onDone: () {
          setState(() {
            _isStreaming = false;
          });
        },
      );
    } catch (e) {
      setState(() {
        _reportContent = '## 错误\n\n无法连接到服务器，请检查网络连接。\n\n错误详情：$e';
        _isLoading = false;
        _isStreaming = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康分析报告'),
        backgroundColor: Colors.blue.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadReport();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadReport();
            },
          ),

          // 报告内容区域
          Expanded(
            child: Container(
              margin: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 报告标题栏
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.analytics,
                          color: Colors.blue.shade700,
                          size: 24,
                        ),
                        SizedBox(width: 8),
                        Text(
                          '健康分析报告',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade800,
                          ),
                        ),
                        Spacer(),
                        if (_isLoading || _isStreaming)
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.blue.shade600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // 报告内容
                  Expanded(
                    child: _isLoading
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(),
                                SizedBox(height: 16),
                                Text(
                                  '正在生成健康报告...',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : SingleChildScrollView(
                            padding: EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Markdown 内容
                                MarkdownBody(
                                  data: _reportContent.isEmpty
                                      ? '正在生成报告，请稍候...'
                                      : _reportContent,
                                  styleSheet: MarkdownStyleSheet(
                                    h1: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade800,
                                    ),
                                    h2: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade700,
                                    ),
                                    h3: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade600,
                                    ),
                                    p: TextStyle(
                                      fontSize: 16,
                                      height: 1.5,
                                      color: Colors.black87,
                                    ),
                                    listBullet: TextStyle(
                                      color: Colors.blue.shade600,
                                    ),
                                  ),
                                ),

                                // 流式加载指示器
                                if (_isStreaming)
                                  Padding(
                                    padding: EdgeInsets.only(top: 16),
                                    child: Row(
                                      children: [
                                        SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                              Colors.blue.shade600,
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          '正在生成中...',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                // 免责声明
                                Container(
                                  margin: EdgeInsets.only(top: 24),
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.amber.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.amber.shade200,
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Icon(
                                        Icons.warning_amber,
                                        color: Colors.amber.shade700,
                                        size: 20,
                                      ),
                                      SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          '该内容由AI生成，请注意甄别。健康分析仅供参考，如有疑惑请咨询专业兽医。',
                                          style: TextStyle(
                                            color: Colors.amber.shade800,
                                            fontSize: 14,
                                            height: 1.4,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
