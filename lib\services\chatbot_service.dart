import 'dart:async';
import 'dart:convert';
import 'package:eventflux/eventflux.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../constants/constants.dart';
import '../providers/auth_provider.dart';
import '../constants/url_form.dart';
import '../utils/app_logger.dart';

/// SSE响应数据模型
class SSEResponseData {
  final String? content;
  final String? error;
  final bool? isComplete; // 标识流是否完成

  SSEResponseData({
    this.content,
    this.error,
    this.isComplete,
  });

  factory SSEResponseData.fromJson(Map<String, dynamic> json) {
    return SSEResponseData(
      content: json['content'],
      error: json['error'],
      isComplete: json['isComplete'] ?? false,
    );
  }

  bool get hasError => error != null && error!.isNotEmpty;
  bool get hasContent => content != null && content!.isNotEmpty;
  bool get isStreamComplete => isComplete == true;
}

/// AI聊天服务
/// 使用POST方法和SSE协议接收服务端流式数据
class ChatbotService {
  static const String _baseUrl = UrlFormat.Chatbot; // 替换为实际的服务端地址

  // 超时配置
  static const Duration _responseTimeout = Duration(seconds: 60);

  final Uuid _uuid = const Uuid();

  /// 当前会话ID
  String? _currentSessionId;

  /// 当前活跃的EventFlux连接
  EventFlux? _currentEventFlux;

  /// 获取当前会话ID，如果没有则生成新的
  String get currentSessionId {
    _currentSessionId ??= _uuid.v4();
    return _currentSessionId!;
  }

  /// 创建新会话
  String createNewSession() {
    _currentSessionId = _uuid.v4();
    AppLogger.info('创建新会话: $_currentSessionId');
    return _currentSessionId!;
  }

  /// 恢复会话
  void restoreSession(String sessionId) {
    _currentSessionId = sessionId;
    AppLogger.info('恢复会话: $_currentSessionId');
  }

  /// 发送消息并返回SSE数据流
  /// [message] 用户输入的消息
  /// [sessionId] 会话ID，如果为null则使用当前会话ID
  Stream<SSEResponseData> sendMessageStream(String message,
      {String? sessionId}) {
    final controller = StreamController<SSEResponseData>();
    final useSessionId = sessionId ?? currentSessionId;
    Timer? timeoutTimer;
    EventFlux? eventFlux;

    // 异步执行，避免阻塞主线程
    Future.microtask(() async {
      try {
        AppLogger.info('发送消息给AI: $message, 会话ID: $useSessionId');

        // 取消之前的连接
        await cancelCurrentRequest();

        // 在测试环境中跳过认证
        String? accessToken;
        try {
          final authProvider = Provider.of<AuthProvider>(
              navigatorKey.currentContext!,
              listen: false);
          accessToken = authProvider.user!.accessToken;
        } catch (e) {
          // 测试环境中使用模拟token
          accessToken = 'test_token';
          AppLogger.warning('使用测试token: $e');
        }

        // 构建POST请求体
        final requestBody = {
          'prompt': message,
          'session_id': useSessionId,
        };

        // 构建请求头
        final headers = {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        };

        AppLogger.info('SSE请求URL: $_baseUrl');
        AppLogger.info('请求体: ${jsonEncode(requestBody)}');

        // 设置响应超时（只在没有收到任何数据时触发）
        bool hasReceivedData = false;
        timeoutTimer = Timer(_responseTimeout, () {
          if (!hasReceivedData) {
            AppLogger.warning('SSE响应超时');
            if (!controller.isClosed) {
              controller.addError('请求超时，请检查网络连接后重试');
              controller.close();
            }
            eventFlux?.disconnect();
          }
        });

        // 创建EventFlux实例
        eventFlux = EventFlux.spawn();

        // 连接到SSE流
        eventFlux!.connect(
          EventFluxConnectionType.post,
          _baseUrl,
          header: headers,
          body: requestBody,
          tag: 'ChatbotSSE',
          logReceivedData: false,
          onSuccessCallback: (EventFluxResponse? response) {
            AppLogger.info('EventFlux连接成功');

            // 监听数据流
            response?.stream?.listen(
              (EventFluxData data) {
                try {
                  AppLogger.info('接收到SSE事件: ${data.data}');

                  // 解析SSE数据
                  if (data.data != null && data.data.isNotEmpty) {
                    hasReceivedData = true; // 标记已收到数据
                    final jsonData = jsonDecode(data.data);
                    final responseData = SSEResponseData.fromJson(jsonData);

                    if (!controller.isClosed) {
                      controller.add(responseData);
                    }
                  }
                } catch (e, stackTrace) {
                  AppLogger.error('解析SSE数据失败: $e',
                      error: e, stackTrace: stackTrace);
                  if (!controller.isClosed) {
                    controller.addError('数据解析错误，请重试');
                  }
                }
              },
              onError: (error) {
                AppLogger.error('SSE数据流错误: $error', error: error);
                timeoutTimer?.cancel();
                if (!controller.isClosed) {
                  controller.addError('数据流错误，请重试');
                }
              },
              onDone: () {
                AppLogger.info('SSE数据流结束');
                timeoutTimer?.cancel();
                if (!controller.isClosed) {
                  controller.close();
                }
              },
            );
          },
          onError: (EventFluxException? error) {
            AppLogger.error('EventFlux连接错误: ${error?.message}',
                error: error ?? 'Unknown error');
            timeoutTimer?.cancel();
            if (!controller.isClosed) {
              controller.addError('网络连接错误，请检查网络后重试');
            }
          },
          onConnectionClose: () {
            AppLogger.info('EventFlux连接关闭');
            timeoutTimer?.cancel();
            if (!controller.isClosed) {
              controller.close();
            }
          },
        );

        // 保存当前连接引用
        _currentEventFlux = eventFlux;
      } catch (e, stackTrace) {
        AppLogger.error('创建SSE连接失败: $e', error: e, stackTrace: stackTrace);
        timeoutTimer?.cancel();
        if (!controller.isClosed) {
          controller.addError('连接失败，请检查网络设置');
        }
      }
    });

    // 当控制器关闭时清理资源
    controller.onCancel = () {
      timeoutTimer?.cancel();
      eventFlux?.disconnect();
      _currentEventFlux = null;
    };

    return controller.stream;
  }

  /// 取消当前请求
  Future<void> cancelCurrentRequest() async {
    if (_currentEventFlux != null) {
      AppLogger.info('取消当前EventFlux连接');
      await _currentEventFlux!.disconnect();
      _currentEventFlux = null;
    }
  }

  /// 释放资源
  void dispose() {
    cancelCurrentRequest();
  }
}
